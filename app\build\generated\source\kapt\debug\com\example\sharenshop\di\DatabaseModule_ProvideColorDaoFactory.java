// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.database.SharenShopDatabase;
import com.example.sharenshop.data.database.dao.ColorDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideColorDaoFactory implements Factory<ColorDao> {
  private final Provider<SharenShopDatabase> databaseProvider;

  public DatabaseModule_ProvideColorDaoFactory(Provider<SharenShopDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public ColorDao get() {
    return provideColorDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideColorDaoFactory create(
      Provider<SharenShopDatabase> databaseProvider) {
    return new DatabaseModule_ProvideColorDaoFactory(databaseProvider);
  }

  public static ColorDao provideColorDao(SharenShopDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideColorDao(database));
  }
}

// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class VariableRepository_Factory implements Factory<VariableRepository> {
  @Override
  public VariableRepository get() {
    return newInstance();
  }

  public static VariableRepository_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static VariableRepository newInstance() {
    return new VariableRepository();
  }

  private static final class InstanceHolder {
    private static final VariableRepository_Factory INSTANCE = new VariableRepository_Factory();
  }
}

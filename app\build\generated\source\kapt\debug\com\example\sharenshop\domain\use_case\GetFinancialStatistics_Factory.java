// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.StatisticsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetFinancialStatistics_Factory implements Factory<GetFinancialStatistics> {
  private final Provider<StatisticsRepository> repositoryProvider;

  public GetFinancialStatistics_Factory(Provider<StatisticsRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public GetFinancialStatistics get() {
    return newInstance(repositoryProvider.get());
  }

  public static GetFinancialStatistics_Factory create(
      Provider<StatisticsRepository> repositoryProvider) {
    return new GetFinancialStatistics_Factory(repositoryProvider);
  }

  public static GetFinancialStatistics newInstance(StatisticsRepository repository) {
    return new GetFinancialStatistics(repository);
  }
}

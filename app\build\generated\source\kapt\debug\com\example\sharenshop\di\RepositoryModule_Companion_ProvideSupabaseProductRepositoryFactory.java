// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.remote.repository.MockSupabaseProductRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_Companion_ProvideSupabaseProductRepositoryFactory implements Factory<MockSupabaseProductRepository> {
  @Override
  public MockSupabaseProductRepository get() {
    return provideSupabaseProductRepository();
  }

  public static RepositoryModule_Companion_ProvideSupabaseProductRepositoryFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static MockSupabaseProductRepository provideSupabaseProductRepository() {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.Companion.provideSupabaseProductRepository());
  }

  private static final class InstanceHolder {
    private static final RepositoryModule_Companion_ProvideSupabaseProductRepositoryFactory INSTANCE = new RepositoryModule_Companion_ProvideSupabaseProductRepositoryFactory();
  }
}

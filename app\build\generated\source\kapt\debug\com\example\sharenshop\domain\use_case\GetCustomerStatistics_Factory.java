// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.StatisticsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetCustomerStatistics_Factory implements Factory<GetCustomerStatistics> {
  private final Provider<StatisticsRepository> repositoryProvider;

  public GetCustomerStatistics_Factory(Provider<StatisticsRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public GetCustomerStatistics get() {
    return newInstance(repositoryProvider.get());
  }

  public static GetCustomerStatistics_Factory create(
      Provider<StatisticsRepository> repositoryProvider) {
    return new GetCustomerStatistics_Factory(repositoryProvider);
  }

  public static GetCustomerStatistics newInstance(StatisticsRepository repository) {
    return new GetCustomerStatistics(repository);
  }
}

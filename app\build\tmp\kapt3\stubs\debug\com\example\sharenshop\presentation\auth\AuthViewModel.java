package com.example.sharenshop.presentation.auth;

import androidx.lifecycle.ViewModel;
import com.example.sharenshop.data.local.SessionManager;
import com.example.sharenshop.data.model.User;
import com.example.sharenshop.data.model.UserRole;
import com.example.sharenshop.data.remote.SupabaseClient;
import com.example.sharenshop.domain.use_case.CustomerApprovalUseCases;
import com.example.sharenshop.domain.use_case.UserUseCases;
import dagger.hilt.android.lifecycle.HiltViewModel;
import kotlinx.coroutines.flow.StateFlow;
import javax.inject.Inject;
import io.github.jan.supabase.postgrest.query.Columns;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\n\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001b\n\u0002\u0010\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0014\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0006\u00108\u001a\u000209J\u000e\u0010:\u001a\u000209H\u0082@\u00a2\u0006\u0002\u0010;J\b\u0010<\u001a\u000209H\u0002J\u0006\u0010=\u001a\u000209J\b\u0010>\u001a\u000209H\u0002J\b\u0010?\u001a\u000209H\u0002J\u0006\u0010@\u001a\u000209J\b\u0010A\u001a\u0004\u0018\u00010\u000bJ\b\u0010B\u001a\u0004\u0018\u00010\u000bJ\b\u0010C\u001a\u0004\u0018\u00010\u000bJ\b\u0010D\u001a\u0004\u0018\u00010EJ\u0006\u0010F\u001a\u000209J\u000e\u0010G\u001a\u0002092\u0006\u0010H\u001a\u00020\u000bJ\u000e\u0010I\u001a\u0002092\u0006\u0010J\u001a\u00020\u000bJ\u000e\u0010K\u001a\u0002092\u0006\u0010L\u001a\u00020\u000bJ\u000e\u0010M\u001a\u0002092\u0006\u0010N\u001a\u00020\u000bJ\u000e\u0010O\u001a\u0002092\u0006\u0010P\u001a\u00020\u000bJ\u000e\u0010Q\u001a\u0002092\u0006\u0010R\u001a\u00020\u000bJ\u000e\u0010S\u001a\u0002092\u0006\u0010T\u001a\u00020\u0019J\u000e\u0010U\u001a\u000209H\u0082@\u00a2\u0006\u0002\u0010;J\u000e\u0010V\u001a\u000209H\u0082@\u00a2\u0006\u0002\u0010;J\b\u0010W\u001a\u000209H\u0002J\u0006\u0010X\u001a\u000209R\u0016\u0010\t\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u000e0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u000e0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u000e0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u000e0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0018\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00190\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190\u001b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u001c\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000b0\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010 \u001a\b\u0012\u0004\u0012\u00020\u000b0\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u001fR\u0017\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u000e0\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u001fR\u0017\u0010#\u001a\b\u0012\u0004\u0012\u00020\u000e0\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u001fR\u0017\u0010$\u001a\b\u0012\u0004\u0012\u00020\u000e0\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u001fR\u0017\u0010%\u001a\b\u0012\u0004\u0012\u00020\u000e0\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u001fR\u0017\u0010&\u001a\b\u0012\u0004\u0012\u00020\u000b0\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001fR\u0017\u0010(\u001a\b\u0012\u0004\u0012\u00020\u000b0\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\u001fR\u0017\u0010*\u001a\b\u0012\u0004\u0012\u00020\u000e0\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010\u001fR\u0017\u0010,\u001a\b\u0012\u0004\u0012\u00020\u000b0\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010\u001fR\u0017\u0010.\u001a\b\u0012\u0004\u0012\u00020\u000b0\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010\u001fR\u0017\u00100\u001a\b\u0012\u0004\u0012\u00020\u000b0\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010\u001fR\u0019\u00102\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00190\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010\u001fR\u001d\u00104\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190\u001b0\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u0010\u001fR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u00107R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006Y"}, d2 = {"Lcom/example/sharenshop/presentation/auth/AuthViewModel;", "Landroidx/lifecycle/ViewModel;", "userUseCases", "Lcom/example/sharenshop/domain/use_case/UserUseCases;", "customerApprovalUseCases", "Lcom/example/sharenshop/domain/use_case/CustomerApprovalUseCases;", "sessionManager", "Lcom/example/sharenshop/data/local/SessionManager;", "(Lcom/example/sharenshop/domain/use_case/UserUseCases;Lcom/example/sharenshop/domain/use_case/CustomerApprovalUseCases;Lcom/example/sharenshop/data/local/SessionManager;)V", "_authError", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_email", "_isAuthenticated", "", "_isCustomerSignupMode", "_isLoading", "_isLoginMode", "_name", "_password", "_pendingApproval", "_phone", "_referrerCode", "_role", "_selectedSeller", "Lcom/example/sharenshop/data/model/User;", "_sellers", "", "authError", "Lkotlinx/coroutines/flow/StateFlow;", "getAuthError", "()Lkotlinx/coroutines/flow/StateFlow;", "email", "getEmail", "isAuthenticated", "isCustomerSignupMode", "isLoading", "isLoginMode", "name", "getName", "password", "getPassword", "pendingApproval", "getPendingApproval", "phone", "getPhone", "referrerCode", "getReferrerCode", "role", "getRole", "selectedSeller", "getSelectedSeller", "sellers", "getSellers", "getSessionManager", "()Lcom/example/sharenshop/data/local/SessionManager;", "authenticate", "", "authenticateUser", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "checkExistingSession", "clearSellerSelection", "clearSignupFields", "fetchSellers", "forceLogout", "getCurrentUserEmail", "getCurrentUserId", "getCurrentUserName", "getCurrentUserRole", "Lcom/example/sharenshop/data/model/UserRole;", "logout", "onEmailChange", "newEmail", "onNameChange", "newName", "onPasswordChange", "newPassword", "onPhoneChange", "newPhone", "onReferrerCodeChange", "newCode", "onRoleChange", "newRole", "onSellerSelected", "seller", "saveUserSession", "submitCustomerApprovalRequest", "testSupabaseConnection", "toggleAuthMode", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class AuthViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.UserUseCases userUseCases = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.CustomerApprovalUseCases customerApprovalUseCases = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.data.local.SessionManager sessionManager = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _email = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> email = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _password = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> password = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _name = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> name = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _phone = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> phone = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _role = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> role = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _referrerCode = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> referrerCode = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.sharenshop.data.model.User> _selectedSeller = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.sharenshop.data.model.User> selectedSeller = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _authError = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> authError = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isAuthenticated = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isAuthenticated = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoginMode = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoginMode = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isCustomerSignupMode = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isCustomerSignupMode = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _pendingApproval = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> pendingApproval = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.sharenshop.data.model.User>> _sellers = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.sharenshop.data.model.User>> sellers = null;
    
    @javax.inject.Inject()
    public AuthViewModel(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.UserUseCases userUseCases, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.CustomerApprovalUseCases customerApprovalUseCases, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.local.SessionManager sessionManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.local.SessionManager getSessionManager() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getEmail() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getPassword() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getPhone() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getRole() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getReferrerCode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.sharenshop.data.model.User> getSelectedSeller() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getAuthError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isAuthenticated() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoginMode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isCustomerSignupMode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getPendingApproval() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.sharenshop.data.model.User>> getSellers() {
        return null;
    }
    
    private final void fetchSellers() {
    }
    
    public final void onEmailChange(@org.jetbrains.annotations.NotNull()
    java.lang.String newEmail) {
    }
    
    public final void onPasswordChange(@org.jetbrains.annotations.NotNull()
    java.lang.String newPassword) {
    }
    
    public final void onNameChange(@org.jetbrains.annotations.NotNull()
    java.lang.String newName) {
    }
    
    public final void onPhoneChange(@org.jetbrains.annotations.NotNull()
    java.lang.String newPhone) {
    }
    
    public final void onRoleChange(@org.jetbrains.annotations.NotNull()
    java.lang.String newRole) {
    }
    
    public final void onReferrerCodeChange(@org.jetbrains.annotations.NotNull()
    java.lang.String newCode) {
    }
    
    public final void onSellerSelected(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.User seller) {
    }
    
    public final void clearSellerSelection() {
    }
    
    public final void toggleAuthMode() {
    }
    
    private final void clearSignupFields() {
    }
    
    public final void authenticate() {
    }
    
    private final java.lang.Object authenticateUser(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object submitCustomerApprovalRequest(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final void testSupabaseConnection() {
    }
    
    private final void checkExistingSession() {
    }
    
    private final java.lang.Object saveUserSession(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    public final void logout() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.sharenshop.data.model.UserRole getCurrentUserRole() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCurrentUserId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCurrentUserEmail() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCurrentUserName() {
        return null;
    }
    
    public final void forceLogout() {
    }
}
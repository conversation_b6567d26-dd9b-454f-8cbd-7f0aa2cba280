// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import io.github.jan.supabase.SupabaseClient;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SupabaseRepository_Factory implements Factory<SupabaseRepository> {
  private final Provider<SupabaseClient> supabaseClientProvider;

  public SupabaseRepository_Factory(Provider<SupabaseClient> supabaseClientProvider) {
    this.supabaseClientProvider = supabaseClientProvider;
  }

  @Override
  public SupabaseRepository get() {
    return newInstance(supabaseClientProvider.get());
  }

  public static SupabaseRepository_Factory create(Provider<SupabaseClient> supabaseClientProvider) {
    return new SupabaseRepository_Factory(supabaseClientProvider);
  }

  public static SupabaseRepository newInstance(SupabaseClient supabaseClient) {
    return new SupabaseRepository(supabaseClient);
  }
}

// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.presentation.auth;

import com.example.sharenshop.data.local.SessionManager;
import com.example.sharenshop.domain.use_case.CustomerApprovalUseCases;
import com.example.sharenshop.domain.use_case.UserUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AuthViewModel_Factory implements Factory<AuthViewModel> {
  private final Provider<UserUseCases> userUseCasesProvider;

  private final Provider<CustomerApprovalUseCases> customerApprovalUseCasesProvider;

  private final Provider<SessionManager> sessionManagerProvider;

  public AuthViewModel_Factory(Provider<UserUseCases> userUseCasesProvider,
      Provider<CustomerApprovalUseCases> customerApprovalUseCasesProvider,
      Provider<SessionManager> sessionManagerProvider) {
    this.userUseCasesProvider = userUseCasesProvider;
    this.customerApprovalUseCasesProvider = customerApprovalUseCasesProvider;
    this.sessionManagerProvider = sessionManagerProvider;
  }

  @Override
  public AuthViewModel get() {
    return newInstance(userUseCasesProvider.get(), customerApprovalUseCasesProvider.get(), sessionManagerProvider.get());
  }

  public static AuthViewModel_Factory create(Provider<UserUseCases> userUseCasesProvider,
      Provider<CustomerApprovalUseCases> customerApprovalUseCasesProvider,
      Provider<SessionManager> sessionManagerProvider) {
    return new AuthViewModel_Factory(userUseCasesProvider, customerApprovalUseCasesProvider, sessionManagerProvider);
  }

  public static AuthViewModel newInstance(UserUseCases userUseCases,
      CustomerApprovalUseCases customerApprovalUseCases, SessionManager sessionManager) {
    return new AuthViewModel(userUseCases, customerApprovalUseCases, sessionManager);
  }
}

// Generated by Da<PERSON> (https://dagger.dev).
package com.example.sharenshop.data.repository;

import android.content.Context;
import com.example.sharenshop.data.local.dao.ProductCategoryDao;
import com.example.sharenshop.data.local.dao.ProductDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import io.github.jan.supabase.SupabaseClient;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProductRepositoryImpl_Factory implements Factory<ProductRepositoryImpl> {
  private final Provider<ProductDao> productDaoProvider;

  private final Provider<ProductCategoryDao> categoryDaoProvider;

  private final Provider<SupabaseClient> supabaseClientProvider;

  private final Provider<Context> contextProvider;

  public ProductRepositoryImpl_Factory(Provider<ProductDao> productDaoProvider,
      Provider<ProductCategoryDao> categoryDaoProvider,
      Provider<SupabaseClient> supabaseClientProvider, Provider<Context> contextProvider) {
    this.productDaoProvider = productDaoProvider;
    this.categoryDaoProvider = categoryDaoProvider;
    this.supabaseClientProvider = supabaseClientProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public ProductRepositoryImpl get() {
    return newInstance(productDaoProvider.get(), categoryDaoProvider.get(), supabaseClientProvider.get(), contextProvider.get());
  }

  public static ProductRepositoryImpl_Factory create(Provider<ProductDao> productDaoProvider,
      Provider<ProductCategoryDao> categoryDaoProvider,
      Provider<SupabaseClient> supabaseClientProvider, Provider<Context> contextProvider) {
    return new ProductRepositoryImpl_Factory(productDaoProvider, categoryDaoProvider, supabaseClientProvider, contextProvider);
  }

  public static ProductRepositoryImpl newInstance(ProductDao productDao,
      ProductCategoryDao categoryDao, SupabaseClient supabaseClient, Context context) {
    return new ProductRepositoryImpl(productDao, categoryDao, supabaseClient, context);
  }
}

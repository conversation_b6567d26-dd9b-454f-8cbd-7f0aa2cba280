// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.database.SharenShopDatabase;
import com.example.sharenshop.data.database.dao.SizeDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideSizeDaoFactory implements Factory<SizeDao> {
  private final Provider<SharenShopDatabase> databaseProvider;

  public DatabaseModule_ProvideSizeDaoFactory(Provider<SharenShopDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public SizeDao get() {
    return provideSizeDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideSizeDaoFactory create(
      Provider<SharenShopDatabase> databaseProvider) {
    return new DatabaseModule_ProvideSizeDaoFactory(databaseProvider);
  }

  public static SizeDao provideSizeDao(SharenShopDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideSizeDao(database));
  }
}

// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.StatisticsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ExportReport_Factory implements Factory<ExportReport> {
  private final Provider<StatisticsRepository> repositoryProvider;

  public ExportReport_Factory(Provider<StatisticsRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public ExportReport get() {
    return newInstance(repositoryProvider.get());
  }

  public static ExportReport_Factory create(Provider<StatisticsRepository> repositoryProvider) {
    return new ExportReport_Factory(repositoryProvider);
  }

  public static ExportReport newInstance(StatisticsRepository repository) {
    return new ExportReport(repository);
  }
}

androidx.room.RoomDatabase2kotlinx.serialization.internal.GeneratedSerializer:com.example.sharenshop.domain.repository.InvoiceRepository:com.example.sharenshop.domain.repository.ProductRepository6com.example.sharenshop.data.security.EncryptionServiceandroidx.lifecycle.ViewModel+com.example.sharenshop.ui.navigation.Screen#androidx.activity.ComponentActivityjava.io.Serializablekotlin.Enumandroid.app.Application#androidx.room.RoomDatabase.Callback"androidx.datastore.core.Serializer7com.example.sharenshop.domain.repository.CartRepositoryCcom.example.sharenshop.domain.repository.CustomerApprovalRepository;com.example.sharenshop.domain.repository.CustomerRepository:com.example.sharenshop.domain.repository.MessageRepository?com.example.sharenshop.domain.repository.NotificationRepository;com.example.sharenshop.domain.repository.SecurityRepository9com.example.sharenshop.domain.repository.SellerRepository;com.example.sharenshop.domain.repository.SettingsRepository=com.example.sharenshop.domain.repository.SharenShopRepository=com.example.sharenshop.domain.repository.StatisticsRepository7com.example.sharenshop.domain.repository.UserRepository                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               
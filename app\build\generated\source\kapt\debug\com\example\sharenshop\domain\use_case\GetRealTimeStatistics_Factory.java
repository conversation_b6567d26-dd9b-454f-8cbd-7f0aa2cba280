// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.StatisticsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetRealTimeStatistics_Factory implements Factory<GetRealTimeStatistics> {
  private final Provider<StatisticsRepository> repositoryProvider;

  public GetRealTimeStatistics_Factory(Provider<StatisticsRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public GetRealTimeStatistics get() {
    return newInstance(repositoryProvider.get());
  }

  public static GetRealTimeStatistics_Factory create(
      Provider<StatisticsRepository> repositoryProvider) {
    return new GetRealTimeStatistics_Factory(repositoryProvider);
  }

  public static GetRealTimeStatistics newInstance(StatisticsRepository repository) {
    return new GetRealTimeStatistics(repository);
  }
}

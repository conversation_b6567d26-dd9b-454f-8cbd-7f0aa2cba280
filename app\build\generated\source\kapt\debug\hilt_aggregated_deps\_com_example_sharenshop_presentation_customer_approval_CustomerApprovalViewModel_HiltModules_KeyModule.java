package hilt_aggregated_deps;

import dagger.hilt.processor.internal.aggregateddeps.AggregatedDeps;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedDeps(
    components = "dagger.hilt.android.components.ActivityRetainedComponent",
    modules = "com.example.sharenshop.presentation.customer_approval.CustomerApprovalViewModel_HiltModules.KeyModule"
)
public class _com_example_sharenshop_presentation_customer_approval_CustomerApprovalViewModel_HiltModules_KeyModule {
}

// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.local.dao.CustomerApprovalDao;
import com.example.sharenshop.data.local.database.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaoModule_ProvideCustomerApprovalDaoFactory implements Factory<CustomerApprovalDao> {
  private final Provider<AppDatabase> appDatabaseProvider;

  public DaoModule_ProvideCustomerApprovalDaoFactory(Provider<AppDatabase> appDatabaseProvider) {
    this.appDatabaseProvider = appDatabaseProvider;
  }

  @Override
  public CustomerApprovalDao get() {
    return provideCustomerApprovalDao(appDatabaseProvider.get());
  }

  public static DaoModule_ProvideCustomerApprovalDaoFactory create(
      Provider<AppDatabase> appDatabaseProvider) {
    return new DaoModule_ProvideCustomerApprovalDaoFactory(appDatabaseProvider);
  }

  public static CustomerApprovalDao provideCustomerApprovalDao(AppDatabase appDatabase) {
    return Preconditions.checkNotNullFromProvides(DaoModule.INSTANCE.provideCustomerApprovalDao(appDatabase));
  }
}

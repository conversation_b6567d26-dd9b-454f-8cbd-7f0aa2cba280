// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.data.language;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class LanguageManager_Factory implements Factory<LanguageManager> {
  private final Provider<Context> contextProvider;

  public LanguageManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public LanguageManager get() {
    return newInstance(contextProvider.get());
  }

  public static LanguageManager_Factory create(Provider<Context> contextProvider) {
    return new LanguageManager_Factory(contextProvider);
  }

  public static LanguageManager newInstance(Context context) {
    return new LanguageManager(context);
  }
}

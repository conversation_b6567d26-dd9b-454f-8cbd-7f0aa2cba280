// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.domain.repository.CartRepository;
import com.example.sharenshop.domain.use_case.CartUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideCartUseCasesFactory implements Factory<CartUseCases> {
  private final Provider<CartRepository> repositoryProvider;

  public UseCaseModule_ProvideCartUseCasesFactory(Provider<CartRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public CartUseCases get() {
    return provideCartUseCases(repositoryProvider.get());
  }

  public static UseCaseModule_ProvideCartUseCasesFactory create(
      Provider<CartRepository> repositoryProvider) {
    return new UseCaseModule_ProvideCartUseCasesFactory(repositoryProvider);
  }

  public static CartUseCases provideCartUseCases(CartRepository repository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideCartUseCases(repository));
  }
}

// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.remote.repository.MockSupabaseInvoiceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_Companion_ProvideSupabaseInvoiceRepositoryFactory implements Factory<MockSupabaseInvoiceRepository> {
  @Override
  public MockSupabaseInvoiceRepository get() {
    return provideSupabaseInvoiceRepository();
  }

  public static RepositoryModule_Companion_ProvideSupabaseInvoiceRepositoryFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static MockSupabaseInvoiceRepository provideSupabaseInvoiceRepository() {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.Companion.provideSupabaseInvoiceRepository());
  }

  private static final class InstanceHolder {
    private static final RepositoryModule_Companion_ProvideSupabaseInvoiceRepositoryFactory INSTANCE = new RepositoryModule_Companion_ProvideSupabaseInvoiceRepositoryFactory();
  }
}

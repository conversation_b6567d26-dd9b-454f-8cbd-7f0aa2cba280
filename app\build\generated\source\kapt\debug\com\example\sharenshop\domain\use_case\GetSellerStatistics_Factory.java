// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.StatisticsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetSellerStatistics_Factory implements Factory<GetSellerStatistics> {
  private final Provider<StatisticsRepository> repositoryProvider;

  public GetSellerStatistics_Factory(Provider<StatisticsRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public GetSellerStatistics get() {
    return newInstance(repositoryProvider.get());
  }

  public static GetSellerStatistics_Factory create(
      Provider<StatisticsRepository> repositoryProvider) {
    return new GetSellerStatistics_Factory(repositoryProvider);
  }

  public static GetSellerStatistics newInstance(StatisticsRepository repository) {
    return new GetSellerStatistics(repository);
  }
}

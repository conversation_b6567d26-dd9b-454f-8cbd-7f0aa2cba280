// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.CustomerApprovalRepository;
import com.example.sharenshop.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ApproveCustomer_Factory implements Factory<ApproveCustomer> {
  private final Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  public ApproveCustomer_Factory(
      Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    this.customerApprovalRepositoryProvider = customerApprovalRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public ApproveCustomer get() {
    return newInstance(customerApprovalRepositoryProvider.get(), userRepositoryProvider.get());
  }

  public static ApproveCustomer_Factory create(
      Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    return new ApproveCustomer_Factory(customerApprovalRepositoryProvider, userRepositoryProvider);
  }

  public static ApproveCustomer newInstance(CustomerApprovalRepository customerApprovalRepository,
      UserRepository userRepository) {
    return new ApproveCustomer(customerApprovalRepository, userRepository);
  }
}

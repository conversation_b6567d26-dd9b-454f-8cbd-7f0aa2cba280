// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.remote.repository.MockSupabaseUserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_Companion_ProvideSupabaseUserRepositoryFactory implements Factory<MockSupabaseUserRepository> {
  @Override
  public MockSupabaseUserRepository get() {
    return provideSupabaseUserRepository();
  }

  public static RepositoryModule_Companion_ProvideSupabaseUserRepositoryFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static MockSupabaseUserRepository provideSupabaseUserRepository() {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.Companion.provideSupabaseUserRepository());
  }

  private static final class InstanceHolder {
    private static final RepositoryModule_Companion_ProvideSupabaseUserRepositoryFactory INSTANCE = new RepositoryModule_Companion_ProvideSupabaseUserRepositoryFactory();
  }
}

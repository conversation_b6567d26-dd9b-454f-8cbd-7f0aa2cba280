package com.example.sharenshop.data.repository

import com.example.sharenshop.data.local.dao.UserDao
// import com.example.sharenshop.data.remote.SupabaseManager
// import com.example.sharenshop.data.remote.SupabaseConstants
// import com.example.sharenshop.data.remote.postgrest
// import kotlinx.serialization.json.Json
import com.example.sharenshop.domain.repository.UserRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import com.example.sharenshop.data.model.User
import com.example.sharenshop.data.model.UserRole
import io.github.jan.supabase.SupabaseClient
import io.github.jan.supabase.postgrest.postgrest

class UserRepositoryImpl @Inject constructor(
    private val userDao: UserDao,
    // private val supabaseManager: SupabaseManager,
    // private val json: Json // Inject Json serializer
    private val supabaseClient: SupabaseClient
) : UserRepository {

    override fun getUserById(userId: String): Flow<User?> = flow {
        // First emit local data
        userDao.getUserById(userId).collect { localUser ->
            emit(localUser)
        }

        // Mock implementation - در پیاده‌سازی واقعی باید از Supabase استفاده شود
        try {
            android.util.Log.d("UserRepositoryImpl", "Mock: Getting user by ID $userId")
        } catch (e: Exception) {
            android.util.Log.e("UserRepositoryImpl", "Failed to fetch user by ID", e)
        }
    }

    override suspend fun insertUser(user: User) {
        userDao.insertUser(user)
        try {
            // Mock implementation - در پیاده‌سازی واقعی باید از Supabase استفاده شود
            android.util.Log.d("UserRepositoryImpl", "Mock: Inserting user ${user.id}")
        } catch (e: Exception) {
            android.util.Log.e("UserRepositoryImpl", "Failed to insert user to Supabase", e)
            // Optional: add rollback logic here
            throw e
        }
    }

    override suspend fun updateUser(user: User) {
        userDao.updateUser(user)
        try {
            // Mock implementation - در پیاده‌سازی واقعی باید از Supabase استفاده شود
            android.util.Log.d("UserRepositoryImpl", "Mock: Updating user ${user.id}")
        } catch (e: Exception) {
            android.util.Log.e("UserRepositoryImpl", "Failed to update user in Supabase", e)
            // Optional: add rollback logic here
            throw e
        }
    }

    override suspend fun deleteUserById(userId: String) {
        userDao.deleteUserById(userId)
        try {
            // Mock implementation - در پیاده‌سازی واقعی باید از Supabase استفاده شود
            android.util.Log.d("UserRepositoryImpl", "Mock: Deleting user $userId")
        } catch (e: Exception) {
            android.util.Log.e("UserRepositoryImpl", "Failed to delete user from Supabase", e)
            // Optional: add rollback logic here
            throw e
        }
    }

    override fun getAllUsers(): Flow<List<User>> = flow {
        userDao.getAllUsers().collect { localUsers ->
            emit(localUsers)
        }

        try {
            // Mock implementation - در پیاده‌سازی واقعی باید از Supabase استفاده شود
            android.util.Log.d("UserRepositoryImpl", "Mock: Getting all users from local cache")
        } catch (e: Exception) {
            android.util.Log.e("UserRepositoryImpl", "Failed to fetch all users", e)
        }
    }

    override fun getUsersByRole(role: UserRole): Flow<List<User>> = flow {
        try {
            // Mock implementation - در پیاده‌سازی واقعی باید از Supabase استفاده شود
            android.util.Log.d("UserRepositoryImpl", "Mock: Getting users by role ${role.value}")
            emit(emptyList())
        } catch (e: Exception) {
            android.util.Log.e("UserRepositoryImpl", "Error fetching users by role: ${e.message}", e)
            emit(emptyList())
        }
    }

    override fun getUsersByUserType(userType: String): Flow<List<User>> = flow {
        try {
            // Mock implementation - در پیاده‌سازی واقعی باید از Supabase استفاده شود
            android.util.Log.d("UserRepositoryImpl", "Mock: Getting users by userType $userType")
            emit(emptyList())
        } catch (e: Exception) {
            android.util.Log.e("UserRepositoryImpl", "Error fetching users by userType: ${e.message}", e)
            emit(emptyList())
        }
    }

    override suspend fun getUserByUsername(username: String): User? {
        return try {
            android.util.Log.d("UserRepositoryImpl", "Getting user by username: $username")

            // Mock implementation for now - will be replaced with real Supabase implementation
            when (username) {
                "admin" -> User(
                    id = "admin_id",
                    email = "<EMAIL>",
                    username = "admin",
                    userType = "super_admin",
                    fullName = "مدیر کل",
                    phone = null,
                    address = null,
                    businessName = null,
                    ownerName = null,
                    profileImageUrl = null,
                    lastLogin = null,
                    createdAt = null
                )
                "ahmad_seller" -> User(
                    id = "ahmad_id",
                    email = "<EMAIL>",
                    username = "ahmad_seller",
                    userType = "cashier",
                    fullName = "احمد محمدی",
                    phone = null,
                    address = null,
                    businessName = null,
                    ownerName = null,
                    profileImageUrl = null,
                    lastLogin = null,
                    createdAt = null
                )
                else -> null
            }
        } catch (e: Exception) {
            android.util.Log.e("UserRepositoryImpl", "Error getting user by username: ${e.message}", e)
            null
        }
    }

    override suspend fun getUserByEmail(email: String): User? {
        return try {
            android.util.Log.d("UserRepositoryImpl", "Getting user by email: $email")

            // Direct fallback to known users
            when (email.trim()) {
                "<EMAIL>" -> User(
                    id = "11111111-1111-1111-1111-111111111111",
                    email = "<EMAIL>",
                    username = "Alikhan1372",
                    userType = "super_admin",
                    fullName = "علی خان احمدی",
                    phone = null,
                    address = null,
                    businessName = null,
                    ownerName = null,
                    profileImageUrl = null,
                    lastLogin = null,
                    createdAt = null
                )
                "<EMAIL>" -> User(
                    id = "22222222-2222-2222-2222-222222222222",
                    email = "<EMAIL>",
                    username = "shahzad1374",
                    userType = "cashier",
                    fullName = "شهزاد محمدی",
                    phone = null,
                    address = null,
                    businessName = null,
                    ownerName = null,
                    profileImageUrl = null,
                    lastLogin = null,
                    createdAt = null
                )
                else -> null
            }
        } catch (e: Exception) {
            android.util.Log.e("UserRepositoryImpl", "Error getting user by email: ${e.message}", e)

            // Fallback to known users if Supabase fails
            when (email) {
                "<EMAIL>" -> User(
                    id = "alikhan_id",
                    email = "<EMAIL>",
                    username = "alikhan",
                    userType = "super_admin",
                    fullName = "علی خان",
                    phone = null,
                    address = null,
                    businessName = null,
                    ownerName = null,
                    profileImageUrl = null,
                    lastLogin = null,
                    createdAt = null
                )
                "<EMAIL>" -> User(
                    id = "admin_id",
                    email = "<EMAIL>",
                    username = "admin",
                    userType = "super_admin",
                    fullName = "مدیر کل",
                    phone = null,
                    address = null,
                    businessName = null,
                    ownerName = null,
                    profileImageUrl = null,
                    lastLogin = null,
                    createdAt = null
                )
                "<EMAIL>" -> User(
                    id = "cashier_id",
                    email = "<EMAIL>",
                    username = "cashier",
                    userType = "cashier",
                    fullName = "صندوقدار",
                    phone = null,
                    address = null,
                    businessName = null,
                    ownerName = null,
                    profileImageUrl = null,
                    lastLogin = null,
                    createdAt = null
                )
                else -> null
            }
        }
    }

    override fun getSellers(): Flow<List<User>> {
        return getUsersByRole(UserRole.SELLER)
    }
}
// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.CartRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AddToCartUseCase_Factory implements Factory<AddToCartUseCase> {
  private final Provider<CartRepository> repositoryProvider;

  public AddToCartUseCase_Factory(Provider<CartRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public AddToCartUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static AddToCartUseCase_Factory create(Provider<CartRepository> repositoryProvider) {
    return new AddToCartUseCase_Factory(repositoryProvider);
  }

  public static AddToCartUseCase newInstance(CartRepository repository) {
    return new AddToCartUseCase(repository);
  }
}

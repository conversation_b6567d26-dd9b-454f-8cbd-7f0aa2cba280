package hilt_aggregated_deps;

import dagger.hilt.processor.internal.aggregateddeps.AggregatedDeps;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedDeps(
    components = "dagger.hilt.android.components.ActivityRetainedComponent",
    modules = "com.example.sharenshop.data.language.LanguageManager_HiltModules.KeyModule"
)
public class _com_example_sharenshop_data_language_LanguageManager_HiltModules_KeyModule {
}

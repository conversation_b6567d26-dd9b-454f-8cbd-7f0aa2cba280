// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.CartRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ClearCartUseCase_Factory implements Factory<ClearCartUseCase> {
  private final Provider<CartRepository> repositoryProvider;

  public ClearCartUseCase_Factory(Provider<CartRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public ClearCartUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static ClearCartUseCase_Factory create(Provider<CartRepository> repositoryProvider) {
    return new ClearCartUseCase_Factory(repositoryProvider);
  }

  public static ClearCartUseCase newInstance(CartRepository repository) {
    return new ClearCartUseCase(repository);
  }
}

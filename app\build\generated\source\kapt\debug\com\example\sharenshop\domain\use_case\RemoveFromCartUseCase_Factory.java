// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.CartRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RemoveFromCartUseCase_Factory implements Factory<RemoveFromCartUseCase> {
  private final Provider<CartRepository> repositoryProvider;

  public RemoveFromCartUseCase_Factory(Provider<CartRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public RemoveFromCartUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static RemoveFromCartUseCase_Factory create(Provider<CartRepository> repositoryProvider) {
    return new RemoveFromCartUseCase_Factory(repositoryProvider);
  }

  public static RemoveFromCartUseCase newInstance(CartRepository repository) {
    return new RemoveFromCartUseCase(repository);
  }
}

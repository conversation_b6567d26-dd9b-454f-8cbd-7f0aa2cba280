// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.presentation.variables;

import com.example.sharenshop.data.repository.ProductRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class VariableViewModel_Factory implements Factory<VariableViewModel> {
  private final Provider<ProductRepository> productRepositoryProvider;

  public VariableViewModel_Factory(Provider<ProductRepository> productRepositoryProvider) {
    this.productRepositoryProvider = productRepositoryProvider;
  }

  @Override
  public VariableViewModel get() {
    return newInstance(productRepositoryProvider.get());
  }

  public static VariableViewModel_Factory create(
      Provider<ProductRepository> productRepositoryProvider) {
    return new VariableViewModel_Factory(productRepositoryProvider);
  }

  public static VariableViewModel newInstance(ProductRepository productRepository) {
    return new VariableViewModel(productRepository);
  }
}

package com.example.sharenshop.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Brush
import com.example.sharenshop.ui.theme.SharenShopColors
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.*
import androidx.compose.animation.fadeIn
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.text.KeyboardOptions
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.sharenshop.data.model.User
import com.example.sharenshop.data.model.UserRole
import com.example.sharenshop.data.model.Language
import com.example.sharenshop.presentation.auth.AuthViewModel
import com.example.sharenshop.ui.components.CustomPasswordField
import com.example.sharenshop.ui.components.EnhancedLoginScreen
import com.example.sharenshop.ui.utils.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AuthScreen(
    viewModel: AuthViewModel = hiltViewModel(),
    onNavigateToMain: () -> Unit = {},
    onNavigateToPendingApproval: () -> Unit = {}
) {
    val email by viewModel.email.collectAsStateWithLifecycle()
    val password by viewModel.password.collectAsStateWithLifecycle()
    val name by viewModel.name.collectAsStateWithLifecycle()
    val phone by viewModel.phone.collectAsStateWithLifecycle()
    val role by viewModel.role.collectAsStateWithLifecycle()
    val referrerCode by viewModel.referrerCode.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    val authError by viewModel.authError.collectAsStateWithLifecycle()
    val isLoginMode by viewModel.isLoginMode.collectAsStateWithLifecycle()
    val isAuthenticated by viewModel.isAuthenticated.collectAsStateWithLifecycle()
    val isCustomerSignupMode by viewModel.isCustomerSignupMode.collectAsStateWithLifecycle()
    val pendingApproval by viewModel.pendingApproval.collectAsStateWithLifecycle()
    val sellers by viewModel.sellers.collectAsStateWithLifecycle()
    val selectedSeller by viewModel.selectedSeller.collectAsStateWithLifecycle()

    // State for user type selection
    var showSellerLogin by remember { mutableStateOf(false) }
    var showCustomerLogin by remember { mutableStateOf(false) }
    var showAdminLogin by remember { mutableStateOf(false) }

    // Navigate to main when authenticated
    LaunchedEffect(isAuthenticated) {
        android.util.Log.d("AuthScreen", "LaunchedEffect triggered, isAuthenticated: $isAuthenticated")
        if (isAuthenticated) {
            android.util.Log.d("AuthScreen", "Navigating to main screen...")
            onNavigateToMain()
        }
    }

    // Navigate to pending approval when needed
    LaunchedEffect(pendingApproval) {
        if (pendingApproval) {
            android.util.Log.d("AuthScreen", "Navigating to pending approval screen...")
            onNavigateToPendingApproval()
        }
    }

    val adaptivePadding = getAdaptivePadding()
    val isLandscapeMode = isLandscape()
    val scrollState = rememberScrollState()

    // لوکس رنگ‌های شارن شاپ
    val luxuryPrimary = SharenShopColors.Primary

    // Main screen with user type selection
    if (!showSellerLogin && !showCustomerLogin && !showAdminLogin) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    Brush.verticalGradient(
                        colors = listOf(
                            SharenShopColors.Background,
                            SharenShopColors.SurfaceVariant,
                            SharenShopColors.Surface
                        )
                    )
                )
                .padding(adaptivePadding)
                .then(
                    if (isLandscapeMode) {
                        Modifier.verticalScroll(scrollState)
                    } else {
                        Modifier
                    }
                ),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = if (isLandscapeMode) Arrangement.Top else Arrangement.Center
        ) {
            Spacer(modifier = Modifier.height(48.dp))

            // App name in luxury style
            Text(
                text = "پوشاک شارن",
                style = MaterialTheme.typography.headlineLarge,
                fontSize = 36.sp,
                fontWeight = FontWeight.Bold,
                color = SharenShopColors.TextPrimary
            )

            Spacer(modifier = Modifier.height(64.dp))

            // Seller login button
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 32.dp)
                    .clickable { showSellerLogin = true },
                colors = CardDefaults.cardColors(
                    containerColor = SharenShopColors.ButtonPrimary
                ),
                shape = RoundedCornerShape(12.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            Brush.horizontalGradient(
                                colors = listOf(
                                    SharenShopColors.Gold,
                                    SharenShopColors.SecondaryVariant
                                )
                            )
                        )
                        .padding(vertical = 16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "فروشنده",
                        color = SharenShopColors.TextOnSecondary,
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.sp
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Customer signup button
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 32.dp)
                    .clickable { showCustomerLogin = true },
                colors = CardDefaults.cardColors(
                    containerColor = SharenShopColors.ButtonSecondary
                ),
                shape = RoundedCornerShape(12.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            Brush.horizontalGradient(
                                colors = listOf(
                                    SharenShopColors.NavyBlue,
                                    SharenShopColors.DarkPurple
                                )
                            )
                        )
                        .padding(vertical = 16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "خریدار",
                        color = SharenShopColors.TextOnPrimary,
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.sp
                    )
                }
            }

            Spacer(modifier = Modifier.height(32.dp))
        }
    } else if (showSellerLogin) {
        // Simple seller login screen
        InstagramStyleLoginScreen(
            title = "ورود فروشنده",
            onBackClick = { showSellerLogin = false },
            onAdminClick = { showAdminLogin = true },
            email = email,
            password = password,
            onEmailChange = { viewModel.onEmailChange(it) },
            onPasswordChange = { viewModel.onPasswordChange(it) },
            onLoginClick = {
                viewModel.onRoleChange(UserRole.SELLER.name)
                viewModel.authenticate()
            },
            isLoading = isLoading,
            authError = authError
        )
    } else if (showCustomerLogin) {
        // Customer signup screen
        CustomerSignupScreen(
            onBackClick = { showCustomerLogin = false },
            name = name,
            email = email,
            phone = phone,
            password = password,
            onNameChange = { viewModel.onNameChange(it) },
            onEmailChange = { viewModel.onEmailChange(it) },
            onPhoneChange = { viewModel.onPhoneChange(it) },
            onPasswordChange = { viewModel.onPasswordChange(it) },
            onSignupClick = {
                viewModel.onRoleChange(UserRole.CUSTOMER.name)
                viewModel.authenticate()
            },
            isLoading = isLoading,
            authError = authError
        )
    } else {
        // Admin login screen with chat animation
        AdminLoginWithChatScreen(
            onBackClick = { showAdminLogin = false },
            email = email,
            password = password,
            onEmailChange = { viewModel.onEmailChange(it) },
            onPasswordChange = { viewModel.onPasswordChange(it) },
            onLoginClick = {
                viewModel.onRoleChange(UserRole.ADMIN.name)
                viewModel.authenticate()
            },
            isLoading = isLoading,
            authError = authError
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InstagramStyleLoginScreen(
    title: String,
    onBackClick: () -> Unit,
    onAdminClick: (() -> Unit)?,
    email: String,
    password: String,
    onEmailChange: (String) -> Unit,
    onPasswordChange: (String) -> Unit,
    onLoginClick: () -> Unit,
    isLoading: Boolean,
    authError: String?
) {
    val luxuryPrimary = SharenShopColors.Primary

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        SharenShopColors.Background,
                        SharenShopColors.Surface
                    )
                )
            )
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Back button
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 16.dp)
        ) {
            IconButton(
                onClick = onBackClick,
                modifier = Modifier.size(48.dp)
            ) {
                Icon(
                    Icons.Default.ArrowBack,
                    contentDescription = "برگشت",
                    tint = SharenShopColors.IconPrimary,
                    modifier = Modifier.size(24.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(48.dp))

        // App name with one-time typing animation
        OneTimeTypewriterText(
            text = "sharenshop",
            style = MaterialTheme.typography.headlineLarge.copy(
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold,
                color = SharenShopColors.TextPrimary
            )
        )

        Spacer(modifier = Modifier.height(48.dp))

        // Email field
        OutlinedTextField(
            value = email,
            onValueChange = onEmailChange,
            label = { Text("ایمیل یا نام کاربری") },
            modifier = Modifier.fillMaxWidth(),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = SharenShopColors.Primary,
                focusedLabelColor = SharenShopColors.Primary,
                unfocusedBorderColor = SharenShopColors.BorderLight
            ),
            shape = RoundedCornerShape(12.dp)
        )

        Spacer(modifier = Modifier.height(12.dp))

        // Password field
        var passwordVisible by remember { mutableStateOf(false) }
        OutlinedTextField(
            value = password,
            onValueChange = onPasswordChange,
            label = { Text("رمز عبور") },
            visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
            trailingIcon = {
                IconButton(onClick = { passwordVisible = !passwordVisible }) {
                    Icon(
                        imageVector = if (passwordVisible) Icons.Default.Visibility else Icons.Default.VisibilityOff,
                        contentDescription = if (passwordVisible) "مخفی کردن رمز" else "نمایش رمز"
                    )
                }
            },
            modifier = Modifier.fillMaxWidth(),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = SharenShopColors.Primary,
                focusedLabelColor = SharenShopColors.Primary,
                unfocusedBorderColor = SharenShopColors.BorderLight
            ),
            shape = RoundedCornerShape(12.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Login button
        Button(
            onClick = onLoginClick,
            modifier = Modifier.fillMaxWidth(),
            enabled = !isLoading,
            colors = ButtonDefaults.buttonColors(
                containerColor = SharenShopColors.ButtonPrimary,
                disabledContainerColor = SharenShopColors.ButtonPrimary.copy(alpha = 0.5f)
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    color = Color.White,
                    strokeWidth = 2.dp
                )
            } else {
                Text(
                    text = "ورود",
                    color = SharenShopColors.TextOnSecondary,
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Admin login link (only show if not on admin screen)
        onAdminClick?.let { adminClick ->
            TextButton(
                onClick = adminClick
            ) {
                Text(
                    text = "مدیر",
                    color = SharenShopColors.Link,
                    fontSize = 14.sp
                )
            }
        }

        // Error message
        authError?.let { error ->
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = error,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AdminLoginWithChatScreen(
    onBackClick: () -> Unit,
    email: String,
    password: String,
    onEmailChange: (String) -> Unit,
    onPasswordChange: (String) -> Unit,
    onLoginClick: () -> Unit,
    isLoading: Boolean,
    authError: String?
) {
    val luxuryPrimary = SharenShopColors.Primary
    var showLoginForm by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        SharenShopColors.Background,
                        SharenShopColors.Surface
                    )
                )
            )
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Back button
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 16.dp)
        ) {
            IconButton(
                onClick = onBackClick,
                modifier = Modifier.size(48.dp)
            ) {
                Icon(
                    Icons.Default.ArrowBack,
                    contentDescription = "برگشت",
                    tint = SharenShopColors.IconPrimary,
                    modifier = Modifier.size(24.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(48.dp))

        // App name with typewriter animation
        OneTimeTypewriterText(
            text = "sharenshop",
            style = MaterialTheme.typography.headlineLarge.copy(
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold,
                color = SharenShopColors.TextPrimary
            ),
            onComplete = { showLoginForm = true }
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Chat animation area
        ChatAnimationArea(
            modifier = Modifier.height(300.dp),
            onChatComplete = { /* Chat completed */ }
        )

        // Login form (appears after chat)
        AnimatedVisibility(
            visible = showLoginForm,
            enter = fadeIn(tween(500)) + slideInVertically(tween(500))
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.height(24.dp))

                // Username field
                OutlinedTextField(
                    value = email,
                    onValueChange = onEmailChange,
                    label = { Text("نام کاربری") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = SharenShopColors.Primary,
                        focusedLabelColor = SharenShopColors.Primary,
                        unfocusedBorderColor = SharenShopColors.BorderLight
                    ),
                    shape = RoundedCornerShape(12.dp)
                )

                Spacer(modifier = Modifier.height(12.dp))

                // Password field
                var adminPasswordVisible by remember { mutableStateOf(false) }
                OutlinedTextField(
                    value = password,
                    onValueChange = onPasswordChange,
                    label = { Text("رمز عبور") },
                    visualTransformation = if (adminPasswordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                    trailingIcon = {
                        IconButton(onClick = { adminPasswordVisible = !adminPasswordVisible }) {
                            Icon(
                                imageVector = if (adminPasswordVisible) Icons.Default.Visibility else Icons.Default.VisibilityOff,
                                contentDescription = if (adminPasswordVisible) "مخفی کردن رمز" else "نمایش رمز"
                            )
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = SharenShopColors.Primary,
                        focusedLabelColor = SharenShopColors.Primary,
                        unfocusedBorderColor = SharenShopColors.BorderLight
                    ),
                    shape = RoundedCornerShape(12.dp)
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Login button
                Button(
                    onClick = onLoginClick,
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isLoading,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = SharenShopColors.ButtonPrimary,
                        disabledContainerColor = SharenShopColors.ButtonPrimary.copy(alpha = 0.5f)
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text(
                            text = "ورود",
                            color = SharenShopColors.TextOnSecondary,
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp
                        )
                    }
                }

                // Error message
                authError?.let { error ->
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = error,
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodyMedium,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                }

                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }
}

@Composable
fun OneTimeTypewriterText(
    text: String,
    style: androidx.compose.ui.text.TextStyle,
    modifier: Modifier = Modifier,
    onComplete: () -> Unit = {}
) {
    var displayedText by remember { mutableStateOf("") }
    var isTypingComplete by remember { mutableStateOf(false) }

    LaunchedEffect(text) {
        if (!isTypingComplete) {
            displayedText = ""
            text.forEachIndexed { index, _ ->
                kotlinx.coroutines.delay(100L)
                displayedText = text.substring(0, index + 1)
            }
            isTypingComplete = true
            kotlinx.coroutines.delay(500L) // Small delay before triggering onComplete
            onComplete()
        }
    }

    Text(
        text = displayedText,
        style = style,
        modifier = modifier
    )
}

@Composable
fun ChatAnimationArea(
    modifier: Modifier = Modifier,
    onChatComplete: () -> Unit = {}
) {
    val chatMessages = listOf(
        ChatMessage("آماده‌ای؟", isFromUser = false),
        ChatMessage("نه!", isFromUser = true),
        ChatMessage("چته باز دوباره تو؟", isFromUser = false),
        ChatMessage("یعنی نمیدونی خودت؟", isFromUser = true),
        ChatMessage("دوست دارم خودت بگی بهم", isFromUser = false),
        ChatMessage("داغونم!!", isFromUser = true),
        ChatMessage("خیلی زود میگذره", isFromUser = false),
        ChatMessage("درد دارم خسته‌ام!", isFromUser = true),
        ChatMessage("داری قوی میشی", isFromUser = false),
        ChatMessage("دیگه نمیکشم", isFromUser = true),
        ChatMessage("من پشتتم هولت میدم", isFromUser = false),
        ChatMessage("درگیر گذشتم!", isFromUser = true),
        ChatMessage("به جلو نگاه کن آیندت جذاب‌تره", isFromUser = false),
        ChatMessage("همه چیمو باختم باید از صفر شروع کنم!", isFromUser = true),
        ChatMessage("نباختی! هزینه تجربه‌ای که به دست آوردی رو دادی", isFromUser = false),
        ChatMessage("میخوام ادامه بدم تو هستی؟", isFromUser = true),
        ChatMessage("همیشه بودم حواست نبوده", isFromUser = false),
        ChatMessage("این‌دفعه فرق میکنه بیشتر حواست به من باشه", isFromUser = true),
        ChatMessage("هست!", isFromUser = false),
        ChatMessage("ممنون خدا", isFromUser = true)
    )

    var visibleMessages by remember { mutableStateOf<List<ChatMessage>>(emptyList()) }
    var currentTypingMessage by remember { mutableStateOf<String?>(null) }
    var currentMessageIndex by remember { mutableStateOf(0) }

    LaunchedEffect(Unit) {
        kotlinx.coroutines.delay(1000L) // Wait a bit before starting chat

        chatMessages.forEachIndexed { index, message ->
            // Show typing indicator
            currentTypingMessage = if (message.isFromUser) "تایپ می‌کند..." else "در حال نوشتن..."
            kotlinx.coroutines.delay((1000..2000).random().toLong()) // Random typing time

            // Hide typing indicator and show message
            currentTypingMessage = null
            visibleMessages = visibleMessages + message
            currentMessageIndex = index

            kotlinx.coroutines.delay(500L) // Pause between messages
        }

        kotlinx.coroutines.delay(1000L)
        onChatComplete()
    }

    LazyColumn(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(16.dp)
    ) {
        items(visibleMessages) { message ->
            ChatBubble(message = message)
        }

        // Show typing indicator
        currentTypingMessage?.let { typingText ->
            item {
                TypingIndicator(
                    text = typingText,
                    isFromUser = chatMessages.getOrNull(currentMessageIndex + 1)?.isFromUser ?: false
                )
            }
        }
    }
}

@Composable
fun ChatBubble(message: ChatMessage) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = if (message.isFromUser) Arrangement.End else Arrangement.Start
    ) {
        Card(
            modifier = Modifier.widthIn(max = 280.dp),
            colors = CardDefaults.cardColors(
                containerColor = if (message.isFromUser)
                    SharenShopColors.Primary else SharenShopColors.SurfaceVariant
            ),
            shape = RoundedCornerShape(
                topStart = 16.dp,
                topEnd = 16.dp,
                bottomStart = if (message.isFromUser) 16.dp else 4.dp,
                bottomEnd = if (message.isFromUser) 4.dp else 16.dp
            )
        ) {
            TypewriterMessage(
                text = message.text,
                textColor = if (message.isFromUser) SharenShopColors.TextOnPrimary else SharenShopColors.TextSecondary,
                modifier = Modifier.padding(12.dp)
            )
        }
    }
}

@Composable
fun TypingIndicator(text: String, isFromUser: Boolean) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = if (isFromUser) Arrangement.End else Arrangement.Start
    ) {
        Card(
            modifier = Modifier.widthIn(max = 200.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFE0E0E0)
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Row(
                modifier = Modifier.padding(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = text,
                    color = Color.Gray,
                    fontSize = 14.sp,
                    fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                )
                Spacer(modifier = Modifier.width(8.dp))
                TypingDots()
            }
        }
    }
}

@Composable
fun TypingDots() {
    val infiniteTransition = rememberInfiniteTransition(label = "typing")
    val alpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(600),
            repeatMode = RepeatMode.Reverse
        ),
        label = "alpha"
    )

    Text(
        text = "...",
        color = Color.Gray.copy(alpha = alpha),
        fontSize = 16.sp
    )
}

@Composable
fun TypewriterMessage(
    text: String,
    textColor: Color,
    modifier: Modifier = Modifier
) {
    var displayedText by remember { mutableStateOf("") }

    LaunchedEffect(text) {
        displayedText = ""
        text.forEachIndexed { index, _ ->
            kotlinx.coroutines.delay(50L) // Fast typing for messages
            displayedText = text.substring(0, index + 1)
        }
    }

    Text(
        text = displayedText,
        color = textColor,
        fontSize = 14.sp,
        modifier = modifier
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CustomerSignupScreen(
    onBackClick: () -> Unit,
    name: String,
    email: String,
    phone: String,
    password: String,
    onNameChange: (String) -> Unit,
    onEmailChange: (String) -> Unit,
    onPhoneChange: (String) -> Unit,
    onPasswordChange: (String) -> Unit,
    onSignupClick: () -> Unit,
    isLoading: Boolean,
    authError: String?
) {
    val instagramBlue = Color(0xFF405DE6)
    val scrollState = rememberScrollState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp)
            .verticalScroll(scrollState),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Back button
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Start
        ) {
            IconButton(
                onClick = onBackClick,
                modifier = Modifier.size(48.dp)
            ) {
                Icon(
                    Icons.Default.ArrowBack,
                    contentDescription = "برگشت",
                    tint = instagramBlue,
                    modifier = Modifier.size(24.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Title
        Text(
            text = "ثبت‌نام خریدار",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = instagramBlue
        )

        Spacer(modifier = Modifier.height(48.dp))

        // Name field
        OutlinedTextField(
            value = name,
            onValueChange = onNameChange,
            label = { Text("نام و نام خانوادگی") },
            modifier = Modifier.fillMaxWidth(),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = instagramBlue,
                focusedLabelColor = instagramBlue,
                unfocusedBorderColor = Color(0xFFDBDBDB)
            ),
            shape = RoundedCornerShape(8.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Phone field
        OutlinedTextField(
            value = phone,
            onValueChange = onPhoneChange,
            label = { Text("شماره موبایل") },
            modifier = Modifier.fillMaxWidth(),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = instagramBlue,
                focusedLabelColor = instagramBlue,
                unfocusedBorderColor = Color(0xFFDBDBDB)
            ),
            shape = RoundedCornerShape(8.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Email field
        OutlinedTextField(
            value = email,
            onValueChange = onEmailChange,
            label = { Text("ایمیل") },
            modifier = Modifier.fillMaxWidth(),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = instagramBlue,
                focusedLabelColor = instagramBlue,
                unfocusedBorderColor = Color(0xFFDBDBDB)
            ),
            shape = RoundedCornerShape(8.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Referrer seller dropdown
        var expanded by remember { mutableStateOf(false) }
        var selectedSeller by remember { mutableStateOf<String?>(null) }
        val activeSellers = listOf(
            "احمد رضایی",
            "فاطمه محمدی",
            "علی حسینی",
            "مریم کریمی",
            "حسن احمدی"
        )

        ExposedDropdownMenuBox(
            expanded = expanded,
            onExpandedChange = { expanded = !expanded }
        ) {
            OutlinedTextField(
                value = selectedSeller ?: "",
                onValueChange = { },
                readOnly = true,
                label = { Text("فروشنده معرف (اختیاری)") },
                trailingIcon = {
                    ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .menuAnchor(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = instagramBlue,
                    focusedLabelColor = instagramBlue,
                    unfocusedBorderColor = Color(0xFFDBDBDB)
                ),
                shape = RoundedCornerShape(8.dp)
            )

            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                DropdownMenuItem(
                    text = { Text("بدون معرف") },
                    onClick = {
                        selectedSeller = null
                        expanded = false
                    }
                )
                activeSellers.forEach { seller ->
                    DropdownMenuItem(
                        text = { Text(seller) },
                        onClick = {
                            selectedSeller = seller
                            expanded = false
                        }
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Password field
        var passwordVisible by remember { mutableStateOf(false) }
        OutlinedTextField(
            value = password,
            onValueChange = onPasswordChange,
            label = { Text("رمز عبور") },
            modifier = Modifier.fillMaxWidth(),
            visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
            trailingIcon = {
                IconButton(onClick = { passwordVisible = !passwordVisible }) {
                    Icon(
                        imageVector = if (passwordVisible) Icons.Default.Visibility else Icons.Default.VisibilityOff,
                        contentDescription = if (passwordVisible) "مخفی کردن رمز" else "نمایش رمز"
                    )
                }
            },
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = instagramBlue,
                focusedLabelColor = instagramBlue,
                unfocusedBorderColor = Color(0xFFDBDBDB)
            ),
            shape = RoundedCornerShape(8.dp)
        )

        Spacer(modifier = Modifier.height(24.dp))

        // Signup button
        Button(
            onClick = onSignupClick,
            modifier = Modifier.fillMaxWidth(),
            enabled = !isLoading,
            colors = ButtonDefaults.buttonColors(
                containerColor = instagramBlue,
                disabledContainerColor = instagramBlue.copy(alpha = 0.5f)
            ),
            shape = RoundedCornerShape(8.dp)
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    color = Color.White,
                    strokeWidth = 2.dp
                )
            } else {
                Text(
                    text = "ثبت‌نام",
                    color = Color.White,
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
            }
        }

        // Error message
        authError?.let { error ->
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = error,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

data class ChatMessage(
    val text: String,
    val isFromUser: Boolean
)
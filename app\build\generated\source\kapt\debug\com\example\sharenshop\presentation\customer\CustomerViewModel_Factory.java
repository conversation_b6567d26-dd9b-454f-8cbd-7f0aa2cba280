// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.presentation.customer;

import com.example.sharenshop.domain.repository.CustomerRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CustomerViewModel_Factory implements Factory<CustomerViewModel> {
  private final Provider<CustomerRepository> customerRepositoryProvider;

  public CustomerViewModel_Factory(Provider<CustomerRepository> customerRepositoryProvider) {
    this.customerRepositoryProvider = customerRepositoryProvider;
  }

  @Override
  public CustomerViewModel get() {
    return newInstance(customerRepositoryProvider.get());
  }

  public static CustomerViewModel_Factory create(
      Provider<CustomerRepository> customerRepositoryProvider) {
    return new CustomerViewModel_Factory(customerRepositoryProvider);
  }

  public static CustomerViewModel newInstance(CustomerRepository customerRepository) {
    return new CustomerViewModel(customerRepository);
  }
}

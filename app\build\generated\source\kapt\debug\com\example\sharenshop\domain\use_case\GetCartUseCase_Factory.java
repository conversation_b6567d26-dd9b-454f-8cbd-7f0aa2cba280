// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.CartRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetCartUseCase_Factory implements Factory<GetCartUseCase> {
  private final Provider<CartRepository> repositoryProvider;

  public GetCartUseCase_Factory(Provider<CartRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public GetCartUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static GetCartUseCase_Factory create(Provider<CartRepository> repositoryProvider) {
    return new GetCartUseCase_Factory(repositoryProvider);
  }

  public static GetCartUseCase newInstance(CartRepository repository) {
    return new GetCartUseCase(repository);
  }
}

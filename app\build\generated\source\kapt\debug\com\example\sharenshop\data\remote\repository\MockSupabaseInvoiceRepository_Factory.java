// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.data.remote.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MockSupabaseInvoiceRepository_Factory implements Factory<MockSupabaseInvoiceRepository> {
  @Override
  public MockSupabaseInvoiceRepository get() {
    return newInstance();
  }

  public static MockSupabaseInvoiceRepository_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static MockSupabaseInvoiceRepository newInstance() {
    return new MockSupabaseInvoiceRepository();
  }

  private static final class InstanceHolder {
    private static final MockSupabaseInvoiceRepository_Factory INSTANCE = new MockSupabaseInvoiceRepository_Factory();
  }
}

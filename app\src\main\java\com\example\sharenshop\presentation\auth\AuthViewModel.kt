package com.example.sharenshop.presentation.auth

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharenshop.data.local.SessionManager
import com.example.sharenshop.data.model.User // اطمینان از وجود این ایمپورت اگر User استفاده می‌شود
import com.example.sharenshop.data.model.UserRole
import com.example.sharenshop.data.remote.SupabaseClient
import com.example.sharenshop.domain.use_case.CustomerApprovalUseCases
import com.example.sharenshop.domain.use_case.UserUseCases
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
// import java.util.UUID // اگر در بخش کامنت شده User استفاده می‌شود
import javax.inject.Inject
import io.github.jan.supabase.postgrest.query.Columns

@HiltViewModel
class AuthViewModel @Inject constructor(
    private val userUseCases: UserUseCases,
    private val customerApprovalUseCases: CustomerApprovalUseCases,
    val sessionManager: SessionManager // public برای دسترسی از UI
//    private val supabaseManager: SupabaseManager // این همچنان کامنت است
) : ViewModel() {

    private val _email = MutableStateFlow("")
    val email: StateFlow<String> = _email.asStateFlow()

    private val _password = MutableStateFlow("")
    val password: StateFlow<String> = _password.asStateFlow()

    private val _name = MutableStateFlow("")
    val name: StateFlow<String> = _name.asStateFlow()

    private val _phone = MutableStateFlow("")
    val phone: StateFlow<String> = _phone.asStateFlow()

    private val _role = MutableStateFlow(UserRole.CUSTOMER.value) // Default role for signup
    val role: StateFlow<String> = _role.asStateFlow()

    private val _referrerCode = MutableStateFlow("") // کد معرف فروشنده
    val referrerCode: StateFlow<String> = _referrerCode.asStateFlow()

    private val _selectedSeller = MutableStateFlow<User?>(null)
    val selectedSeller: StateFlow<User?> = _selectedSeller.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _authError = MutableStateFlow<String?>(null)
    val authError: StateFlow<String?> = _authError.asStateFlow()

    private val _isAuthenticated = MutableStateFlow(false)
    val isAuthenticated: StateFlow<Boolean> = _isAuthenticated.asStateFlow()

    private val _isLoginMode = MutableStateFlow(true) // true for login, false for signup
    val isLoginMode: StateFlow<Boolean> = _isLoginMode.asStateFlow()

    private val _isCustomerSignupMode = MutableStateFlow(false) // حالت ثبت نام مشتری
    val isCustomerSignupMode: StateFlow<Boolean> = _isCustomerSignupMode.asStateFlow()

    private val _pendingApproval = MutableStateFlow(false) // حالت انتظار تایید
    val pendingApproval: StateFlow<Boolean> = _pendingApproval.asStateFlow()

    private val _sellers = MutableStateFlow<List<User>>(emptyList())
    val sellers: StateFlow<List<User>> = _sellers.asStateFlow()

    init {
        // تست اتصال Supabase در شروع
        testSupabaseConnection()

        // بررسی session موجود
        checkExistingSession()

        // واکشی لیست فروشندگان
        fetchSellers()
    }

    private fun fetchSellers() {
        viewModelScope.launch {
            userUseCases.getSellers().collect { sellerList ->
                _sellers.value = sellerList
            }
        }
    }

    fun onEmailChange(newEmail: String) {
        _email.value = newEmail
    }

    fun onPasswordChange(newPassword: String) {
        _password.value = newPassword
    }

    fun onNameChange(newName: String) {
        _name.value = newName
    }

    fun onPhoneChange(newPhone: String) {
        _phone.value = newPhone
    }

    fun onRoleChange(newRole: String) {
        _role.value = newRole
    }

    fun onReferrerCodeChange(newCode: String) {
        _referrerCode.value = newCode
    }

    fun onSellerSelected(seller: User) {
        _selectedSeller.value = seller
        _referrerCode.value = seller.id // ذخیره ID فروشنده به عنوان کد معرف
    }

    fun clearSellerSelection() {
        _selectedSeller.value = null
        _referrerCode.value = ""
    }

    fun toggleAuthMode() {
        when {
            _isLoginMode.value -> {
                // از ورود به ثبت نام مشتری
                _isLoginMode.value = false
                _isCustomerSignupMode.value = true
            }
            _isCustomerSignupMode.value -> {
                // از ثبت نام مشتری به ورود
                _isLoginMode.value = true
                _isCustomerSignupMode.value = false
                clearSignupFields()
            }
            else -> {
                // حالت پیش‌فرض: ورود
                _isLoginMode.value = true
                _isCustomerSignupMode.value = false
                clearSignupFields()
            }
        }
        _authError.value = null
    }

    private fun clearSignupFields() {
        _name.value = ""
        _phone.value = ""
        _referrerCode.value = ""
        _role.value = UserRole.CUSTOMER.value
        _selectedSeller.value = null
    }

    fun authenticate() {
        viewModelScope.launch {
            _isLoading.value = true
            _authError.value = null

            try {
                android.util.Log.d("AuthViewModel", "Starting authentication...")
                android.util.Log.d("AuthViewModel", "Email: ${_email.value}")
                android.util.Log.d("AuthViewModel", "IsLoginMode: ${_isLoginMode.value}")
                android.util.Log.d("AuthViewModel", "IsCustomerSignupMode: ${_isCustomerSignupMode.value}")

                when {
                    _isLoginMode.value -> {
                        // ورود مدیر/فروشنده/مشتری تایید شده
                        authenticateUser()
                    }
                    _isCustomerSignupMode.value -> {
                        // ثبت نام مشتری با درخواست تایید
                        submitCustomerApprovalRequest()
                    }
                    else -> {
                        _authError.value = "حالت نامعتبر"
                    }
                }

            } catch (e: Exception) {
                android.util.Log.e("AuthViewModel", "Authentication failed", e)
                _authError.value = e.localizedMessage ?: "خطای احراز هویت"
            }
            _isLoading.value = false
        }
    }

    private suspend fun authenticateUser() {
        android.util.Log.d("AuthViewModel", "🔄 شروع احراز هویت...")
        android.util.Log.d("AuthViewModel", "📧 Email: ${_email.value}")
        android.util.Log.d("AuthViewModel", "🔐 Password length: ${_password.value.length}")

        try {
            // بررسی تنظیمات Supabase
            if (!SupabaseClient.isConfigured()) {
                android.util.Log.e("AuthViewModel", "❌ تنظیمات Supabase ناقص است")
                throw Exception("تنظیمات Supabase ناقص است")
            }

            // تست اتصال به Supabase
            android.util.Log.d("AuthViewModel", "🔄 تست اتصال به Supabase...")
            val connectionTest = SupabaseClient.testConnection()
            if (!connectionTest) {
                android.util.Log.w("AuthViewModel", "⚠️ تست اتصال ناموفق، ادامه با احتیاط...")
            }

            // ورود با Supabase
            android.util.Log.d("AuthViewModel", "🔄 ارسال درخواست ورود به Supabase...")
            android.util.Log.d("AuthViewModel", "📧 Attempting login with email: ${_email.value.trim()}")

            val authResult = SupabaseClient.auth.signInWith(io.github.jan.supabase.gotrue.providers.builtin.Email) {
                email = _email.value.trim()
                password = _password.value
            }

            android.util.Log.d("AuthViewModel", "✅ ورود به Supabase موفق بود")
            android.util.Log.d("AuthViewModel", "🔑 Session created successfully")
            android.util.Log.d("AuthViewModel", "🔍 Auth result: $authResult")

            // دریافت اطلاعات کاربر فعلی
            val currentUser = SupabaseClient.auth.currentUserOrNull()
            android.util.Log.d("AuthViewModel", "👤 User ID: ${currentUser?.id}")
            android.util.Log.d("AuthViewModel", "📧 User Email: ${currentUser?.email}")
            android.util.Log.d("AuthViewModel", "✅ Email Confirmed: ${currentUser?.emailConfirmedAt != null}")

            // بررسی وجود کاربر در جدول users و validation نقش
            android.util.Log.d("AuthViewModel", "🔄 بررسی اطلاعات کاربر در پایگاه داده...")
            try {
                val userFromDb = userUseCases.getUserByEmail(_email.value.trim())

                if (userFromDb == null) {
                    android.util.Log.e("AuthViewModel", "❌ کاربر در پایگاه داده یافت نشد")
                    throw Exception("کاربر در سیستم یافت نشد")
                }

                // بررسی نقش کاربر با نقش درخواستی
                val expectedRole = _role.value
                val actualUserType = userFromDb.userType

                android.util.Log.d("AuthViewModel", "🔍 Expected role: $expectedRole")
                android.util.Log.d("AuthViewModel", "🔍 Actual user_type: $actualUserType")

                val isRoleValid = when (expectedRole) {
                    UserRole.ADMIN.name -> actualUserType == "super_admin"
                    UserRole.SELLER.name -> actualUserType == "cashier"
                    UserRole.CUSTOMER.name -> actualUserType == "customer"
                    else -> false
                }

                if (!isRoleValid) {
                    android.util.Log.e("AuthViewModel", "❌ نقش کاربر مطابقت ندارد")
                    throw Exception("شما مجاز به ورود از این بخش نیستید")
                }

                android.util.Log.d("AuthViewModel", "✅ نقش کاربر تایید شد: $actualUserType")

            } catch (dbError: Exception) {
                android.util.Log.e("AuthViewModel", "❌ خطا در بررسی کاربر: ${dbError.message}")
                throw dbError
            }

            // ذخیره session کاربر
            saveUserSession()

            _isAuthenticated.value = true
            android.util.Log.d("AuthViewModel", "✅ احراز هویت کامل شد")

        } catch (e: Exception) {
            android.util.Log.e("AuthViewModel", "❌ خطای احراز هویت: ${e.message}")
            android.util.Log.e("AuthViewModel", "❌ Exception type: ${e.javaClass.simpleName}")
            android.util.Log.e("AuthViewModel", "❌ Stack trace: ", e)

            when {
                e.message?.contains("Invalid login credentials") == true ||
                e.message?.contains("invalid_credentials") == true -> {
                    _authError.value = "ایمیل یا رمز عبور اشتباه است"
                }
                e.message?.contains("Email not confirmed") == true ||
                e.message?.contains("email_not_confirmed") == true -> {
                    _authError.value = "ایمیل تایید نشده است"
                }
                e.message?.contains("Account pending approval") == true -> {
                    _authError.value = "حساب شما در انتظار تایید است"
                    _pendingApproval.value = true
                }
                e.message?.contains("network") == true ||
                e.message?.contains("timeout") == true ||
                e.message?.contains("connection") == true ||
                e.message?.contains("Unable to resolve host") == true -> {
                    _authError.value = "مشکل در اتصال به اینترنت"
                }
                e.message?.contains("User not found") == true -> {
                    _authError.value = "کاربری با این ایمیل یافت نشد"
                }
                e.message?.contains("Database error") == true ||
                e.message?.contains("schema") == true -> {
                    _authError.value = "خطا در اتصال به پایگاه داده"
                }
                else -> {
                    _authError.value = "خطا در ورود: ${e.message ?: "خطای نامشخص"}"
                }
            }
        }
    }

    private suspend fun submitCustomerApprovalRequest() {
        android.util.Log.d("AuthViewModel", "Submitting customer approval request using UseCase...")

        if (_name.value.isBlank() || _phone.value.isBlank() || _selectedSeller.value == null) {
            _authError.value = "لطفاً تمام فیلدها را کامل کنید"
            return
        }

        try {
            _isLoading.value = true

            val result = customerApprovalUseCases.submitApprovalRequest(
                customerEmail = _email.value.trim(),
                customerName = _name.value.trim(),
                customerPhone = _phone.value.trim(),
                referrerCode = _selectedSeller.value!!.username ?: _selectedSeller.value!!.id // Use username or ID
            )

            result.onSuccess {
        android.util.Log.d("AuthViewModel", "Customer approval request submitted successfully!")
                _authError.value = "✅ درخواست شما با موفقیت ارسال شد. منتظر تایید فروشنده بمانید."
                _pendingApproval.value = true
            }.onFailure { exception ->
                 android.util.Log.e("AuthViewModel", "Error submitting approval request", exception)
                _authError.value = "خطا: ${exception.message}"
            }

        } catch (e: Exception) {
            android.util.Log.e("AuthViewModel", "Unexpected error in submitCustomerApprovalRequest", e)
            _authError.value = "یک خطای پیش‌بینی نشده رخ داد."
        } finally {
            _isLoading.value = false
        }
    }

    private fun testSupabaseConnection() {
        android.util.Log.d("AuthViewModel", "🔄 تست اتصال Supabase...")

        try {
            val isConfigured = SupabaseClient.isConfigured()
            android.util.Log.d("AuthViewModel", "✅ Supabase configured: $isConfigured")

            if (!isConfigured) {
                android.util.Log.e("AuthViewModel", "❌ Supabase URL یا Key خالی است!")
            }

        } catch (e: Exception) {
            android.util.Log.e("AuthViewModel", "❌ خطای تست Supabase: ${e.message}")
        }
    }

    // بررسی session موجود
    private fun checkExistingSession() {
        viewModelScope.launch {
            try {
                android.util.Log.d("AuthViewModel", "🔄 بررسی session موجود...")

                if (sessionManager.isValidSession()) {
                    android.util.Log.d("AuthViewModel", "✅ Session معتبر پیدا شد")
                    _isAuthenticated.value = true
                } else {
                    android.util.Log.d("AuthViewModel", "❌ Session معتبر پیدا نشد")
                    sessionManager.clearSession() // پاک کردن session نامعتبر
                }

            } catch (e: Exception) {
                android.util.Log.e("AuthViewModel", "❌ خطا در بررسی session: ${e.message}")
                sessionManager.clearSession()
            }
        }
    }

    // ذخیره session کاربر
    private suspend fun saveUserSession() {
        try {
            android.util.Log.d("AuthViewModel", "🔄 شروع ذخیره session...")

            // دریافت اطلاعات کاربر فعلی از Supabase Auth
            val currentUser = SupabaseClient.auth.currentUserOrNull()
            val currentSession = SupabaseClient.auth.currentSessionOrNull()

            if (currentUser == null) {
                android.util.Log.e("AuthViewModel", "❌ کاربر فعلی یافت نشد")
                throw Exception("کاربر فعلی یافت نشد")
            }

            android.util.Log.d("AuthViewModel", "👤 User ID: ${currentUser.id}")
            android.util.Log.d("AuthViewModel", "📧 User Email: ${currentUser.email}")

            // تلاش برای دریافت اطلاعات کاربر از جدول users
            var userName = _name.value.ifBlank { currentUser.email?.substringBefore("@") ?: "کاربر" }
            var userRole = UserRole.CUSTOMER

            try {
                android.util.Log.d("AuthViewModel", "🔄 تعیین نقش کاربر از پایگاه داده...")

                // دریافت اطلاعات کاربر از پایگاه داده
                val userFromDb = userUseCases.getUserByEmail(currentUser.email ?: "")

                if (userFromDb != null) {
                    userName = userFromDb.username
                    userRole = when (userFromDb.userType) {
                        "super_admin" -> UserRole.ADMIN
                        "cashier" -> UserRole.SELLER
                        "customer" -> UserRole.CUSTOMER
                        else -> UserRole.CUSTOMER
                    }
                    android.util.Log.d("AuthViewModel", "✅ نقش کاربر از DB: ${userFromDb.userType} -> $userRole")
                } else {
                    android.util.Log.w("AuthViewModel", "⚠️ کاربر در DB یافت نشد، استفاده از نقش پیش‌فرض")
                    userRole = UserRole.CUSTOMER
                }

            } catch (dbError: Exception) {
                android.util.Log.w("AuthViewModel", "⚠️ خطا در تعیین نقش کاربر: ${dbError.message}")
                // Use default role
                userRole = UserRole.CUSTOMER
            }

            android.util.Log.d("AuthViewModel", "🔍 Final Email: ${currentUser.email}")
            android.util.Log.d("AuthViewModel", "🔍 Final Name: $userName")
            android.util.Log.d("AuthViewModel", "🔍 Final Role: $userRole")

            sessionManager.saveUserSession(
                email = currentUser.email ?: _email.value,
                name = userName,
                role = userRole,
                userId = currentUser.id,
                accessToken = currentSession?.accessToken,
                refreshToken = currentSession?.refreshToken
            )

            android.util.Log.d("AuthViewModel", "✅ Session ذخیره شد با نقش: $userRole")

        } catch (e: Exception) {
            android.util.Log.e("AuthViewModel", "❌ خطا در ذخیره session: ${e.message}", e)
            // Fallback: save basic session info
            try {
                val currentUser = SupabaseClient.auth.currentUserOrNull()
                if (currentUser != null) {
                    sessionManager.saveUserSession(
                        email = currentUser.email ?: _email.value,
                        name = _name.value.ifBlank { currentUser.email?.substringBefore("@") ?: "کاربر" },
                        role = if (currentUser.email == "<EMAIL>") UserRole.ADMIN else UserRole.CUSTOMER,
                        userId = currentUser.id,
                        accessToken = null,
                        refreshToken = null
                    )
                    android.util.Log.d("AuthViewModel", "✅ Fallback session ذخیره شد")
                }
            } catch (fallbackError: Exception) {
                android.util.Log.e("AuthViewModel", "❌ خطا در ذخیره fallback session: ${fallbackError.message}")
            }
        }
    }

    // خروج از حساب
    fun logout() {
        viewModelScope.launch {
            try {
                android.util.Log.d("AuthViewModel", "🔄 خروج از حساب...")

                // خروج از Supabase
                SupabaseClient.auth.signOut()

                // پاک کردن session محلی
                sessionManager.clearSession()

                // ریست کردن state ها
                _isAuthenticated.value = false
                _email.value = ""
                _password.value = ""
                _name.value = ""
                _phone.value = ""
                _referrerCode.value = ""
                _authError.value = null

                android.util.Log.d("AuthViewModel", "✅ خروج موفق")

            } catch (e: Exception) {
                android.util.Log.e("AuthViewModel", "❌ خطا در خروج: ${e.message}")
                // حتی اگر خطا باشه، session محلی رو پاک کن
                sessionManager.clearSession()
                _isAuthenticated.value = false
            }
        }
    }

    // دریافت نقش کاربر فعلی
    fun getCurrentUserRole(): UserRole? {
        val role = sessionManager.getUserRole()
        android.util.Log.d("AuthViewModel", "🔍 Current User Role: $role")
        android.util.Log.d("AuthViewModel", "🔍 Current User Email: ${sessionManager.getUserEmail()}")
        return role
    }

    // دریافت شناسه کاربر فعلی
    fun getCurrentUserId(): String? {
        return sessionManager.getUserId()
    }

    // دریافت ایمیل کاربر فعلی
    fun getCurrentUserEmail(): String? {
        return sessionManager.getUserEmail()
    }

    // دریافت نام کاربر فعلی
    fun getCurrentUserName(): String? {
        return sessionManager.getUserName()
    }

    // Force logout برای تست
    fun forceLogout() {
        android.util.Log.d("AuthViewModel", "🔄 Force logout...")
        sessionManager.clearSession()
        _isAuthenticated.value = false
        _email.value = ""
        _password.value = ""
        android.util.Log.d("AuthViewModel", "✅ Force logout completed")
    }
}
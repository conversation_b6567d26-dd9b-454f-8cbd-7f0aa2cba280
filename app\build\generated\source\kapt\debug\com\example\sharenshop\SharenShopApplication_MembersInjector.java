// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop;

import com.example.sharenshop.data.database.SharenShopDatabase;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SharenShopApplication_MembersInjector implements MembersInjector<SharenShopApplication> {
  private final Provider<SharenShopDatabase> databaseProvider;

  public SharenShopApplication_MembersInjector(Provider<SharenShopDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  public static MembersInjector<SharenShopApplication> create(
      Provider<SharenShopDatabase> databaseProvider) {
    return new SharenShopApplication_MembersInjector(databaseProvider);
  }

  @Override
  public void injectMembers(SharenShopApplication instance) {
    injectDatabase(instance, databaseProvider.get());
  }

  @InjectedFieldSignature("com.example.sharenshop.SharenShopApplication.database")
  public static void injectDatabase(SharenShopApplication instance, SharenShopDatabase database) {
    instance.database = database;
  }
}

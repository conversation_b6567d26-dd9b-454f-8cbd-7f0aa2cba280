// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.database.SharenShopDatabase;
import com.example.sharenshop.data.database.dao.ProductVariantDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideProductVariantDaoFactory implements Factory<ProductVariantDao> {
  private final Provider<SharenShopDatabase> databaseProvider;

  public DatabaseModule_ProvideProductVariantDaoFactory(
      Provider<SharenShopDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public ProductVariantDao get() {
    return provideProductVariantDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideProductVariantDaoFactory create(
      Provider<SharenShopDatabase> databaseProvider) {
    return new DatabaseModule_ProvideProductVariantDaoFactory(databaseProvider);
  }

  public static ProductVariantDao provideProductVariantDao(SharenShopDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideProductVariantDao(database));
  }
}

package com.example.sharenshop.data.database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import com.example.sharenshop.data.database.dao.ColorDao;
import com.example.sharenshop.data.database.dao.ColorDao_Impl;
import com.example.sharenshop.data.database.dao.InventoryTransactionDao;
import com.example.sharenshop.data.database.dao.InventoryTransactionDao_Impl;
import com.example.sharenshop.data.database.dao.ProductDao;
import com.example.sharenshop.data.database.dao.ProductDao_Impl;
import com.example.sharenshop.data.database.dao.ProductVariantDao;
import com.example.sharenshop.data.database.dao.ProductVariantDao_Impl;
import com.example.sharenshop.data.database.dao.SizeDao;
import com.example.sharenshop.data.database.dao.SizeDao_Impl;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@SuppressWarnings({"unchecked", "deprecation"})
public final class SharenShopDatabase_Impl extends SharenShopDatabase {
  private volatile ProductDao _productDao;

  private volatile ProductVariantDao _productVariantDao;

  private volatile ColorDao _colorDao;

  private volatile SizeDao _sizeDao;

  private volatile InventoryTransactionDao _inventoryTransactionDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(1) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `products` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL, `code` TEXT NOT NULL, `category` TEXT NOT NULL, `brand` TEXT NOT NULL, `material` TEXT NOT NULL, `description` TEXT, `purchase_price_rial` INTEGER NOT NULL, `sale_price_rial` INTEGER NOT NULL, `discount_percent` REAL NOT NULL, `final_price_rial` INTEGER NOT NULL, `total_quantity` INTEGER NOT NULL, `available_quantity` INTEGER NOT NULL, `reserved_quantity` INTEGER NOT NULL, `sold_quantity` INTEGER NOT NULL, `has_image` INTEGER NOT NULL, `image_path` TEXT, `is_active` INTEGER NOT NULL, `created_by` TEXT NOT NULL, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `product_variants` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `product_id` INTEGER NOT NULL, `color_id` INTEGER NOT NULL, `size_id` INTEGER NOT NULL, `quantity` INTEGER NOT NULL, `available_quantity` INTEGER NOT NULL, `reserved_quantity` INTEGER NOT NULL, `sold_quantity` INTEGER NOT NULL, `is_active` INTEGER NOT NULL, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL, FOREIGN KEY(`product_id`) REFERENCES `products`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`color_id`) REFERENCES `colors`(`id`) ON UPDATE NO ACTION ON DELETE RESTRICT , FOREIGN KEY(`size_id`) REFERENCES `sizes`(`id`) ON UPDATE NO ACTION ON DELETE RESTRICT )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_product_variants_product_id` ON `product_variants` (`product_id`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_product_variants_color_id` ON `product_variants` (`color_id`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_product_variants_size_id` ON `product_variants` (`size_id`)");
        db.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_product_variants_product_id_color_id_size_id` ON `product_variants` (`product_id`, `color_id`, `size_id`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `colors` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name_persian` TEXT NOT NULL, `name_english` TEXT NOT NULL, `hex_code` TEXT NOT NULL, `category` TEXT NOT NULL, `is_primary` INTEGER NOT NULL, `parent_color_id` INTEGER, `is_active` INTEGER NOT NULL, `created_by` TEXT NOT NULL, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `sizes` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL, `display_name` TEXT NOT NULL, `category` TEXT NOT NULL, `sort_order` INTEGER NOT NULL, `measurements` TEXT, `is_active` INTEGER NOT NULL, `created_by` TEXT NOT NULL, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `inventory_transactions` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `variant_id` INTEGER NOT NULL, `transaction_type` TEXT NOT NULL, `quantity_change` INTEGER NOT NULL, `quantity_before` INTEGER NOT NULL, `quantity_after` INTEGER NOT NULL, `reason` TEXT NOT NULL, `reference_id` TEXT, `performed_by` TEXT NOT NULL, `created_at` INTEGER NOT NULL, FOREIGN KEY(`variant_id`) REFERENCES `product_variants`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_inventory_transactions_variant_id` ON `inventory_transactions` (`variant_id`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_inventory_transactions_transaction_type` ON `inventory_transactions` (`transaction_type`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_inventory_transactions_created_at` ON `inventory_transactions` (`created_at`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'c008c8c908bd2f4953a9dd2824586126')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `products`");
        db.execSQL("DROP TABLE IF EXISTS `product_variants`");
        db.execSQL("DROP TABLE IF EXISTS `colors`");
        db.execSQL("DROP TABLE IF EXISTS `sizes`");
        db.execSQL("DROP TABLE IF EXISTS `inventory_transactions`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        db.execSQL("PRAGMA foreign_keys = ON");
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsProducts = new HashMap<String, TableInfo.Column>(21);
        _columnsProducts.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("code", new TableInfo.Column("code", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("category", new TableInfo.Column("category", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("brand", new TableInfo.Column("brand", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("material", new TableInfo.Column("material", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("purchase_price_rial", new TableInfo.Column("purchase_price_rial", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("sale_price_rial", new TableInfo.Column("sale_price_rial", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("discount_percent", new TableInfo.Column("discount_percent", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("final_price_rial", new TableInfo.Column("final_price_rial", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("total_quantity", new TableInfo.Column("total_quantity", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("available_quantity", new TableInfo.Column("available_quantity", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("reserved_quantity", new TableInfo.Column("reserved_quantity", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("sold_quantity", new TableInfo.Column("sold_quantity", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("has_image", new TableInfo.Column("has_image", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("image_path", new TableInfo.Column("image_path", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("is_active", new TableInfo.Column("is_active", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("created_by", new TableInfo.Column("created_by", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProducts.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysProducts = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesProducts = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoProducts = new TableInfo("products", _columnsProducts, _foreignKeysProducts, _indicesProducts);
        final TableInfo _existingProducts = TableInfo.read(db, "products");
        if (!_infoProducts.equals(_existingProducts)) {
          return new RoomOpenHelper.ValidationResult(false, "products(com.example.sharenshop.data.database.entity.ProductEntity).\n"
                  + " Expected:\n" + _infoProducts + "\n"
                  + " Found:\n" + _existingProducts);
        }
        final HashMap<String, TableInfo.Column> _columnsProductVariants = new HashMap<String, TableInfo.Column>(11);
        _columnsProductVariants.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProductVariants.put("product_id", new TableInfo.Column("product_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProductVariants.put("color_id", new TableInfo.Column("color_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProductVariants.put("size_id", new TableInfo.Column("size_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProductVariants.put("quantity", new TableInfo.Column("quantity", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProductVariants.put("available_quantity", new TableInfo.Column("available_quantity", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProductVariants.put("reserved_quantity", new TableInfo.Column("reserved_quantity", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProductVariants.put("sold_quantity", new TableInfo.Column("sold_quantity", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProductVariants.put("is_active", new TableInfo.Column("is_active", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProductVariants.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProductVariants.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysProductVariants = new HashSet<TableInfo.ForeignKey>(3);
        _foreignKeysProductVariants.add(new TableInfo.ForeignKey("products", "CASCADE", "NO ACTION", Arrays.asList("product_id"), Arrays.asList("id")));
        _foreignKeysProductVariants.add(new TableInfo.ForeignKey("colors", "RESTRICT", "NO ACTION", Arrays.asList("color_id"), Arrays.asList("id")));
        _foreignKeysProductVariants.add(new TableInfo.ForeignKey("sizes", "RESTRICT", "NO ACTION", Arrays.asList("size_id"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesProductVariants = new HashSet<TableInfo.Index>(4);
        _indicesProductVariants.add(new TableInfo.Index("index_product_variants_product_id", false, Arrays.asList("product_id"), Arrays.asList("ASC")));
        _indicesProductVariants.add(new TableInfo.Index("index_product_variants_color_id", false, Arrays.asList("color_id"), Arrays.asList("ASC")));
        _indicesProductVariants.add(new TableInfo.Index("index_product_variants_size_id", false, Arrays.asList("size_id"), Arrays.asList("ASC")));
        _indicesProductVariants.add(new TableInfo.Index("index_product_variants_product_id_color_id_size_id", true, Arrays.asList("product_id", "color_id", "size_id"), Arrays.asList("ASC", "ASC", "ASC")));
        final TableInfo _infoProductVariants = new TableInfo("product_variants", _columnsProductVariants, _foreignKeysProductVariants, _indicesProductVariants);
        final TableInfo _existingProductVariants = TableInfo.read(db, "product_variants");
        if (!_infoProductVariants.equals(_existingProductVariants)) {
          return new RoomOpenHelper.ValidationResult(false, "product_variants(com.example.sharenshop.data.database.entity.ProductVariantEntity).\n"
                  + " Expected:\n" + _infoProductVariants + "\n"
                  + " Found:\n" + _existingProductVariants);
        }
        final HashMap<String, TableInfo.Column> _columnsColors = new HashMap<String, TableInfo.Column>(11);
        _columnsColors.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsColors.put("name_persian", new TableInfo.Column("name_persian", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsColors.put("name_english", new TableInfo.Column("name_english", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsColors.put("hex_code", new TableInfo.Column("hex_code", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsColors.put("category", new TableInfo.Column("category", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsColors.put("is_primary", new TableInfo.Column("is_primary", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsColors.put("parent_color_id", new TableInfo.Column("parent_color_id", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsColors.put("is_active", new TableInfo.Column("is_active", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsColors.put("created_by", new TableInfo.Column("created_by", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsColors.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsColors.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysColors = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesColors = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoColors = new TableInfo("colors", _columnsColors, _foreignKeysColors, _indicesColors);
        final TableInfo _existingColors = TableInfo.read(db, "colors");
        if (!_infoColors.equals(_existingColors)) {
          return new RoomOpenHelper.ValidationResult(false, "colors(com.example.sharenshop.data.database.entity.ColorEntity).\n"
                  + " Expected:\n" + _infoColors + "\n"
                  + " Found:\n" + _existingColors);
        }
        final HashMap<String, TableInfo.Column> _columnsSizes = new HashMap<String, TableInfo.Column>(10);
        _columnsSizes.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSizes.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSizes.put("display_name", new TableInfo.Column("display_name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSizes.put("category", new TableInfo.Column("category", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSizes.put("sort_order", new TableInfo.Column("sort_order", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSizes.put("measurements", new TableInfo.Column("measurements", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSizes.put("is_active", new TableInfo.Column("is_active", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSizes.put("created_by", new TableInfo.Column("created_by", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSizes.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSizes.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysSizes = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesSizes = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoSizes = new TableInfo("sizes", _columnsSizes, _foreignKeysSizes, _indicesSizes);
        final TableInfo _existingSizes = TableInfo.read(db, "sizes");
        if (!_infoSizes.equals(_existingSizes)) {
          return new RoomOpenHelper.ValidationResult(false, "sizes(com.example.sharenshop.data.database.entity.SizeEntity).\n"
                  + " Expected:\n" + _infoSizes + "\n"
                  + " Found:\n" + _existingSizes);
        }
        final HashMap<String, TableInfo.Column> _columnsInventoryTransactions = new HashMap<String, TableInfo.Column>(10);
        _columnsInventoryTransactions.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsInventoryTransactions.put("variant_id", new TableInfo.Column("variant_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsInventoryTransactions.put("transaction_type", new TableInfo.Column("transaction_type", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsInventoryTransactions.put("quantity_change", new TableInfo.Column("quantity_change", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsInventoryTransactions.put("quantity_before", new TableInfo.Column("quantity_before", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsInventoryTransactions.put("quantity_after", new TableInfo.Column("quantity_after", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsInventoryTransactions.put("reason", new TableInfo.Column("reason", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsInventoryTransactions.put("reference_id", new TableInfo.Column("reference_id", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsInventoryTransactions.put("performed_by", new TableInfo.Column("performed_by", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsInventoryTransactions.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysInventoryTransactions = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysInventoryTransactions.add(new TableInfo.ForeignKey("product_variants", "CASCADE", "NO ACTION", Arrays.asList("variant_id"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesInventoryTransactions = new HashSet<TableInfo.Index>(3);
        _indicesInventoryTransactions.add(new TableInfo.Index("index_inventory_transactions_variant_id", false, Arrays.asList("variant_id"), Arrays.asList("ASC")));
        _indicesInventoryTransactions.add(new TableInfo.Index("index_inventory_transactions_transaction_type", false, Arrays.asList("transaction_type"), Arrays.asList("ASC")));
        _indicesInventoryTransactions.add(new TableInfo.Index("index_inventory_transactions_created_at", false, Arrays.asList("created_at"), Arrays.asList("ASC")));
        final TableInfo _infoInventoryTransactions = new TableInfo("inventory_transactions", _columnsInventoryTransactions, _foreignKeysInventoryTransactions, _indicesInventoryTransactions);
        final TableInfo _existingInventoryTransactions = TableInfo.read(db, "inventory_transactions");
        if (!_infoInventoryTransactions.equals(_existingInventoryTransactions)) {
          return new RoomOpenHelper.ValidationResult(false, "inventory_transactions(com.example.sharenshop.data.database.entity.InventoryTransactionEntity).\n"
                  + " Expected:\n" + _infoInventoryTransactions + "\n"
                  + " Found:\n" + _existingInventoryTransactions);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "c008c8c908bd2f4953a9dd2824586126", "628e710f1140763b090792204c16b9fa");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "products","product_variants","colors","sizes","inventory_transactions");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    final boolean _supportsDeferForeignKeys = android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP;
    try {
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = FALSE");
      }
      super.beginTransaction();
      if (_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA defer_foreign_keys = TRUE");
      }
      _db.execSQL("DELETE FROM `products`");
      _db.execSQL("DELETE FROM `product_variants`");
      _db.execSQL("DELETE FROM `colors`");
      _db.execSQL("DELETE FROM `sizes`");
      _db.execSQL("DELETE FROM `inventory_transactions`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = TRUE");
      }
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(ProductDao.class, ProductDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(ProductVariantDao.class, ProductVariantDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(ColorDao.class, ColorDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(SizeDao.class, SizeDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(InventoryTransactionDao.class, InventoryTransactionDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public ProductDao productDao() {
    if (_productDao != null) {
      return _productDao;
    } else {
      synchronized(this) {
        if(_productDao == null) {
          _productDao = new ProductDao_Impl(this);
        }
        return _productDao;
      }
    }
  }

  @Override
  public ProductVariantDao productVariantDao() {
    if (_productVariantDao != null) {
      return _productVariantDao;
    } else {
      synchronized(this) {
        if(_productVariantDao == null) {
          _productVariantDao = new ProductVariantDao_Impl(this);
        }
        return _productVariantDao;
      }
    }
  }

  @Override
  public ColorDao colorDao() {
    if (_colorDao != null) {
      return _colorDao;
    } else {
      synchronized(this) {
        if(_colorDao == null) {
          _colorDao = new ColorDao_Impl(this);
        }
        return _colorDao;
      }
    }
  }

  @Override
  public SizeDao sizeDao() {
    if (_sizeDao != null) {
      return _sizeDao;
    } else {
      synchronized(this) {
        if(_sizeDao == null) {
          _sizeDao = new SizeDao_Impl(this);
        }
        return _sizeDao;
      }
    }
  }

  @Override
  public InventoryTransactionDao inventoryTransactionDao() {
    if (_inventoryTransactionDao != null) {
      return _inventoryTransactionDao;
    } else {
      synchronized(this) {
        if(_inventoryTransactionDao == null) {
          _inventoryTransactionDao = new InventoryTransactionDao_Impl(this);
        }
        return _inventoryTransactionDao;
      }
    }
  }
}

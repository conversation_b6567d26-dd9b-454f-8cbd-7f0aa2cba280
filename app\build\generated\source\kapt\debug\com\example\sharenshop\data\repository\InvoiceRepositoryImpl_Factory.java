// Generated by Dagger (https://dagger.dev).
package com.example.sharenshop.data.repository;

import com.example.sharenshop.data.local.dao.InvoiceDao;
import com.example.sharenshop.data.local.dao.InvoiceItemDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import io.github.jan.supabase.SupabaseClient;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class InvoiceRepositoryImpl_Factory implements Factory<InvoiceRepositoryImpl> {
  private final Provider<InvoiceDao> invoiceDaoProvider;

  private final Provider<InvoiceItemDao> invoiceItemDaoProvider;

  private final Provider<SupabaseClient> supabaseClientProvider;

  public InvoiceRepositoryImpl_Factory(Provider<InvoiceDao> invoiceDaoProvider,
      Provider<InvoiceItemDao> invoiceItemDaoProvider,
      Provider<SupabaseClient> supabaseClientProvider) {
    this.invoiceDaoProvider = invoiceDaoProvider;
    this.invoiceItemDaoProvider = invoiceItemDaoProvider;
    this.supabaseClientProvider = supabaseClientProvider;
  }

  @Override
  public InvoiceRepositoryImpl get() {
    return newInstance(invoiceDaoProvider.get(), invoiceItemDaoProvider.get(), supabaseClientProvider.get());
  }

  public static InvoiceRepositoryImpl_Factory create(Provider<InvoiceDao> invoiceDaoProvider,
      Provider<InvoiceItemDao> invoiceItemDaoProvider,
      Provider<SupabaseClient> supabaseClientProvider) {
    return new InvoiceRepositoryImpl_Factory(invoiceDaoProvider, invoiceItemDaoProvider, supabaseClientProvider);
  }

  public static InvoiceRepositoryImpl newInstance(InvoiceDao invoiceDao,
      InvoiceItemDao invoiceItemDao, SupabaseClient supabaseClient) {
    return new InvoiceRepositoryImpl(invoiceDao, invoiceItemDao, supabaseClient);
  }
}

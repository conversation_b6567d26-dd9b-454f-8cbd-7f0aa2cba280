// Generated by Dagger (https://dagger.dev).
package com.example.sharenshop.data.repository;

import com.example.sharenshop.data.local.dao.CartDao;
import com.example.sharenshop.data.local.dao.ProductDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import io.github.jan.supabase.SupabaseClient;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CartRepositoryImpl_Factory implements Factory<CartRepositoryImpl> {
  private final Provider<CartDao> cartDaoProvider;

  private final Provider<ProductDao> productDaoProvider;

  private final Provider<SupabaseClient> supabaseClientProvider;

  public CartRepositoryImpl_Factory(Provider<CartDao> cartDaoProvider,
      Provider<ProductDao> productDaoProvider, Provider<SupabaseClient> supabaseClientProvider) {
    this.cartDaoProvider = cartDaoProvider;
    this.productDaoProvider = productDaoProvider;
    this.supabaseClientProvider = supabaseClientProvider;
  }

  @Override
  public CartRepositoryImpl get() {
    return newInstance(cartDaoProvider.get(), productDaoProvider.get(), supabaseClientProvider.get());
  }

  public static CartRepositoryImpl_Factory create(Provider<CartDao> cartDaoProvider,
      Provider<ProductDao> productDaoProvider, Provider<SupabaseClient> supabaseClientProvider) {
    return new CartRepositoryImpl_Factory(cartDaoProvider, productDaoProvider, supabaseClientProvider);
  }

  public static CartRepositoryImpl newInstance(CartDao cartDao, ProductDao productDao,
      SupabaseClient supabaseClient) {
    return new CartRepositoryImpl(cartDao, productDao, supabaseClient);
  }
}

// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.data.remote.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MockSupabaseUserRepository_Factory implements Factory<MockSupabaseUserRepository> {
  @Override
  public MockSupabaseUserRepository get() {
    return newInstance();
  }

  public static MockSupabaseUserRepository_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static MockSupabaseUserRepository newInstance() {
    return new MockSupabaseUserRepository();
  }

  private static final class InstanceHolder {
    private static final MockSupabaseUserRepository_Factory INSTANCE = new MockSupabaseUserRepository_Factory();
  }
}

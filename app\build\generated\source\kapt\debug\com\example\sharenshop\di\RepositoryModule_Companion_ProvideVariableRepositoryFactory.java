// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.repository.VariableRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_Companion_ProvideVariableRepositoryFactory implements Factory<VariableRepository> {
  @Override
  public VariableRepository get() {
    return provideVariableRepository();
  }

  public static RepositoryModule_Companion_ProvideVariableRepositoryFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static VariableRepository provideVariableRepository() {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.Companion.provideVariableRepository());
  }

  private static final class InstanceHolder {
    private static final RepositoryModule_Companion_ProvideVariableRepositoryFactory INSTANCE = new RepositoryModule_Companion_ProvideVariableRepositoryFactory();
  }
}

// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.data.repository;

import com.example.sharenshop.data.local.dao.MessageDao;
import com.example.sharenshop.data.session.SessionManager;
import com.example.sharenshop.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MessageRepositoryImpl_Factory implements Factory<MessageRepositoryImpl> {
  private final Provider<MessageDao> messageDaoProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  private final Provider<SessionManager> sessionManagerProvider;

  public MessageRepositoryImpl_Factory(Provider<MessageDao> messageDaoProvider,
      Provider<UserRepository> userRepositoryProvider,
      Provider<SessionManager> sessionManagerProvider) {
    this.messageDaoProvider = messageDaoProvider;
    this.userRepositoryProvider = userRepositoryProvider;
    this.sessionManagerProvider = sessionManagerProvider;
  }

  @Override
  public MessageRepositoryImpl get() {
    return newInstance(messageDaoProvider.get(), userRepositoryProvider.get(), sessionManagerProvider.get());
  }

  public static MessageRepositoryImpl_Factory create(Provider<MessageDao> messageDaoProvider,
      Provider<UserRepository> userRepositoryProvider,
      Provider<SessionManager> sessionManagerProvider) {
    return new MessageRepositoryImpl_Factory(messageDaoProvider, userRepositoryProvider, sessionManagerProvider);
  }

  public static MessageRepositoryImpl newInstance(MessageDao messageDao,
      UserRepository userRepository, SessionManager sessionManager) {
    return new MessageRepositoryImpl(messageDao, userRepository, sessionManager);
  }
}

// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import android.content.Context;
import com.example.sharenshop.data.database.SharenShopDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideSharenShopDatabaseFactory implements Factory<SharenShopDatabase> {
  private final Provider<Context> contextProvider;

  public DatabaseModule_ProvideSharenShopDatabaseFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public SharenShopDatabase get() {
    return provideSharenShopDatabase(contextProvider.get());
  }

  public static DatabaseModule_ProvideSharenShopDatabaseFactory create(
      Provider<Context> contextProvider) {
    return new DatabaseModule_ProvideSharenShopDatabaseFactory(contextProvider);
  }

  public static SharenShopDatabase provideSharenShopDatabase(Context context) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideSharenShopDatabase(context));
  }
}

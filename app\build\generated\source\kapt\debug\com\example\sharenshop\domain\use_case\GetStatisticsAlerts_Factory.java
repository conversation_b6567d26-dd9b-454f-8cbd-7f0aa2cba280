// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.StatisticsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetStatisticsAlerts_Factory implements Factory<GetStatisticsAlerts> {
  private final Provider<StatisticsRepository> repositoryProvider;

  public GetStatisticsAlerts_Factory(Provider<StatisticsRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public GetStatisticsAlerts get() {
    return newInstance(repositoryProvider.get());
  }

  public static GetStatisticsAlerts_Factory create(
      Provider<StatisticsRepository> repositoryProvider) {
    return new GetStatisticsAlerts_Factory(repositoryProvider);
  }

  public static GetStatisticsAlerts newInstance(StatisticsRepository repository) {
    return new GetStatisticsAlerts(repository);
  }
}

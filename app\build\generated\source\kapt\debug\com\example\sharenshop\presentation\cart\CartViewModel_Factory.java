// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.presentation.cart;

import com.example.sharenshop.domain.use_case.CartUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import io.github.jan.supabase.SupabaseClient;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CartViewModel_Factory implements Factory<CartViewModel> {
  private final Provider<CartUseCases> cartUseCasesProvider;

  private final Provider<SupabaseClient> supabaseClientProvider;

  public CartViewModel_Factory(Provider<CartUseCases> cartUseCasesProvider,
      Provider<SupabaseClient> supabaseClientProvider) {
    this.cartUseCasesProvider = cartUseCasesProvider;
    this.supabaseClientProvider = supabaseClientProvider;
  }

  @Override
  public CartViewModel get() {
    return newInstance(cartUseCasesProvider.get(), supabaseClientProvider.get());
  }

  public static CartViewModel_Factory create(Provider<CartUseCases> cartUseCasesProvider,
      Provider<SupabaseClient> supabaseClientProvider) {
    return new CartViewModel_Factory(cartUseCasesProvider, supabaseClientProvider);
  }

  public static CartViewModel newInstance(CartUseCases cartUseCases,
      SupabaseClient supabaseClient) {
    return new CartViewModel(cartUseCases, supabaseClient);
  }
}

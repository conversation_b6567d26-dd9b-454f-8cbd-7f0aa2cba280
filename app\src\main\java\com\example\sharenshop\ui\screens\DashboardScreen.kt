package com.example.sharenshop.ui.screens

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.draw.shadow
import com.example.sharenshop.ui.theme.SharenShopColors
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.sharenshop.data.local.SessionManager
import com.example.sharenshop.data.model.Language
import com.example.sharenshop.data.model.UserRole
import com.example.sharenshop.presentation.main.MainViewModel

/**
 * صفحه داشبورد مدیر کل - طراحی اینستاگرام
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DashboardScreen(
    userRole: UserRole,
    currentLanguage: Language,
    onNavigateToVariables: () -> Unit = {},
    onNavigateToProductManagement: () -> Unit = {},
    onNavigateToAddProduct: () -> Unit = {},
    onNavigateToProductStats: () -> Unit = {},
    onNavigateToStatistics: () -> Unit = {},
    onNavigateToSellerPayments: () -> Unit = {},
    onNavigateToSellerManagement: () -> Unit = {},
    onNavigateToCustomerManagement: () -> Unit = {},
    onNavigateToMessages: () -> Unit = {},
    onNavigateToSellerStats: (String) -> Unit = {},
    onNavigateToSellerCustomers: (String) -> Unit = {},
    onNavigateToSellProduct: (String) -> Unit = {},
    onNavigateToPaymentManagement: (String) -> Unit = {},
    onLanguageChange: (Language) -> Unit = {},
    viewModel: MainViewModel = hiltViewModel(),
    modifier: Modifier = Modifier
) {
    var showUserMenu by remember { mutableStateOf(false) }
    var showHamburgerMenu by remember { mutableStateOf(false) }
    var showLanguageDialog by remember { mutableStateOf(false) }

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        SharenShopColors.Background,
                        SharenShopColors.SurfaceVariant
                    )
                )
            )
            .statusBarsPadding() // اضافه کردن padding برای status bar
            .padding(top = 8.dp) // padding اضافی از بالا
    ) {
        // هدر اصلی (طراحی اینستاگرام)
        AdminDashboardHeader(
            userRole = userRole,
            onUserMenuClick = { showUserMenu = true },
            onHamburgerMenuClick = { showHamburgerMenu = true }
        )

        Spacer(modifier = Modifier.height(12.dp))

        // استوری‌های مدیریت (۵ تا)
        AdminStoriesSection(
            onProductsClick = onNavigateToProductManagement,
            onSellersClick = onNavigateToSellerManagement,
            onCustomersClick = onNavigateToCustomerManagement,
            onMessagesClick = onNavigateToMessages,
            onNavigateToStatistics = onNavigateToStatistics,
            onSellerCustomersClick = onNavigateToSellerCustomers,
            onSellProductClick = onNavigateToSellProduct
        )

        Spacer(modifier = Modifier.height(24.dp))

        // ۳ کارت اصلی پایین
        AdminMainCardsSection(
            onAddProductClick = onNavigateToAddProduct,
            onProductStatsClick = onNavigateToProductStats,
            onSellerPaymentsClick = onNavigateToSellerPayments,
            onSellerStatsClick = onNavigateToSellerStats
        )

        Spacer(modifier = Modifier.height(16.dp))
    }

    // منوی کاربری
    if (showUserMenu) {
        UserProfileMenu(
            userRole = userRole,
            onDismiss = { showUserMenu = false }
        )
    }

    // منوی همبرگری
    if (showHamburgerMenu) {
        HamburgerMenu(
            onDismiss = { showHamburgerMenu = false },
            onNavigateToVariables = {
                showHamburgerMenu = false
                onNavigateToVariables()
            },
            onNavigateToSellerCustomers = { sellerId ->
                showHamburgerMenu = false
                onNavigateToSellerCustomers(sellerId)
            },
            onNavigateToPaymentManagement = { sellerId ->
                showHamburgerMenu = false
                onNavigateToPaymentManagement(sellerId)
            },
            onLanguageClick = {
                showHamburgerMenu = false
                showLanguageDialog = true
            }
        )
    }

    // دیالوگ انتخاب زبان
    if (showLanguageDialog) {
        LanguageSelectionDialog(
            currentLanguage = currentLanguage,
            onLanguageSelected = { language ->
                onLanguageChange(language)
                showLanguageDialog = false
            },
            onDismiss = { showLanguageDialog = false }
        )
    }
}

/**
 * هدر اصلی داشبورد مدیر کل - طراحی اینستاگرام
 */
@Composable
fun AdminDashboardHeader(
    userRole: UserRole,
    onUserMenuClick: () -> Unit,
    onHamburgerMenuClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(
                Brush.horizontalGradient(
                    colors = listOf(
                        SharenShopColors.Surface,
                        SharenShopColors.SurfaceVariant
                    )
                )
            )
            .padding(horizontal = 20.dp, vertical = 16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // منوی همبرگری (سمت چپ)
        IconButton(
            onClick = onHamburgerMenuClick,
            modifier = Modifier
                .size(40.dp)
                .background(
                    SharenShopColors.Primary.copy(alpha = 0.1f),
                    CircleShape
                )
        ) {
            Icon(
                Icons.Default.Menu,
                contentDescription = "منو",
                tint = SharenShopColors.Primary,
                modifier = Modifier.size(24.dp)
            )
        }

        // عنوان شارن شاپ (وسط)
        Text(
            text = "شارن شاپ",
            style = MaterialTheme.typography.headlineMedium.copy(
                fontSize = 28.sp,
                fontWeight = FontWeight.Bold
            ),
            color = SharenShopColors.TextPrimary,
            textAlign = TextAlign.Center
        )

        // آیکون حساب کاربری و نقش (سمت راست)
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // نقش کاربر
            Text(
                text = when (userRole) {
                    UserRole.ADMIN -> "مدیر کل"
                    UserRole.SELLER -> "فروشنده"
                    UserRole.CUSTOMER -> "خریدار"
                },
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = SharenShopColors.TextSecondary
            )

            // آیکون حساب کاربری
            IconButton(
                onClick = onUserMenuClick,
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        Brush.radialGradient(
                            colors = listOf(
                                SharenShopColors.Gold.copy(alpha = 0.2f),
                                SharenShopColors.RoseGold.copy(alpha = 0.1f)
                            )
                        ),
                        CircleShape
                    )
            ) {
                Icon(
                    Icons.Default.AccountCircle,
                    contentDescription = "حساب کاربری",
                    tint = SharenShopColors.IconPrimary,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}

/**
 * بخش استوری‌های مدیریت (۵ دایره)
 */
@Composable
fun AdminStoriesSection(
    onProductsClick: () -> Unit = {},
    onSellersClick: () -> Unit = {},
    onCustomersClick: () -> Unit = {},
    onMessagesClick: () -> Unit = {},
    onNavigateToStatistics: () -> Unit = {},
    onSellerCustomersClick: (String) -> Unit = {},
    onSellProductClick: (String) -> Unit = {},
    modifier: Modifier = Modifier
) {
    val stories = listOf(
        StoryItem("محصولات", Icons.Default.Inventory, SharenShopColors.Gold),
        StoryItem("فروشندگان", Icons.Default.Person, SharenShopColors.NavyBlue),
        StoryItem("خریداران", Icons.Default.People, SharenShopColors.RoseGold),
        StoryItem("پیام‌ها", Icons.Default.Message, SharenShopColors.DarkPurple),
        StoryItem("آمار", Icons.Default.Analytics, SharenShopColors.MochaMousse)
    )

    LazyRow(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp),
        horizontalArrangement = Arrangement.spacedBy(24.dp),
        contentPadding = PaddingValues(horizontal = 4.dp)
    ) {
        items(stories) { story ->
            AdminStoryCircle(
                title = story.title,
                icon = story.icon,
                color = story.color,
                onClick = {
                    when (story.title) {
                        "محصولات" -> onProductsClick()
                        "فروشندگان" -> onSellersClick()
                        "خریداران" -> onCustomersClick()
                        "پیام‌ها" -> onMessagesClick()
                        "آمار" -> onNavigateToStatistics()
                        else -> { /* Navigate to other sections */ }
                    }
                }
            )
        }
    }
}

/**
 * دایره استوری مدیریت
 */
@Composable
fun AdminStoryCircle(
    title: String,
    icon: ImageVector,
    color: Color,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.clickable { onClick() },
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Box(
            modifier = Modifier
                .size(70.dp)
                .background(
                    Brush.radialGradient(
                        colors = listOf(
                            color,
                            color.copy(alpha = 0.8f),
                            color.copy(alpha = 0.6f)
                        )
                    ),
                    CircleShape
                )
                .border(3.dp, SharenShopColors.Surface, CircleShape)
                .shadow(
                    elevation = 8.dp,
                    shape = CircleShape,
                    ambientColor = SharenShopColors.ShadowMedium,
                    spotColor = SharenShopColors.ShadowMedium
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                modifier = Modifier.size(32.dp),
                tint = Color.White
            )
        }

        Text(
            text = title,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium,
            color = SharenShopColors.TextSecondary,
            textAlign = TextAlign.Center,
            maxLines = 2,
            modifier = Modifier.width(80.dp)
        )
    }
}

/**
 * مدل داده استوری
 */
data class StoryItem(
    val title: String,
    val icon: ImageVector,
    val color: Color
)

/**
 * بخش ۳ کارت اصلی پایین
 */
@Composable
fun AdminMainCardsSection(
    onAddProductClick: () -> Unit = {},
    onProductStatsClick: () -> Unit = {},
    onSellerPaymentsClick: () -> Unit = {},
    onSellerStatsClick: (String) -> Unit = {},
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        AdminMainCard(
            title = "پرداختی فروشنده",
            icon = Icons.Default.Payment,
            color = SharenShopColors.Success,
            onClick = onSellerPaymentsClick,
            modifier = Modifier.weight(1f)
        )

        AdminMainCard(
            title = "ایجاد محصول جدید",
            icon = Icons.Default.Add,
            color = SharenShopColors.Gold,
            onClick = onAddProductClick,
            modifier = Modifier.weight(1f)
        )

        AdminMainCard(
            title = "آمار محصولات",
            icon = Icons.Default.Analytics,
            color = SharenShopColors.DarkPurple,
            onClick = onProductStatsClick,
            modifier = Modifier.weight(1f)
        )
    }
}

/**
 * کارت اصلی مدیریت
 */
@Composable
fun AdminMainCard(
    title: String,
    icon: ImageVector,
    color: Color,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .height(140.dp)
            .shadow(
                elevation = 12.dp,
                shape = RoundedCornerShape(16.dp),
                ambientColor = SharenShopColors.ShadowMedium,
                spotColor = SharenShopColors.ShadowMedium
            ),
        onClick = onClick,
        colors = CardDefaults.cardColors(
            containerColor = SharenShopColors.Surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    Brush.verticalGradient(
                        colors = listOf(
                            color.copy(alpha = 0.05f),
                            color.copy(alpha = 0.15f)
                        )
                    )
                )
                .padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                modifier = Modifier.size(40.dp),
                tint = color
            )

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                color = SharenShopColors.TextSecondary,
                textAlign = TextAlign.Center,
                maxLines = 2
            )
        }
    }
}

/**
 * منوی حساب کاربری
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserProfileMenu(
    userRole: UserRole,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    sessionManager: SessionManager = hiltViewModel<MainViewModel>().sessionManager
) {
    // دریافت اطلاعات کاربر فعلی از session
    val userName = sessionManager.getUserName() ?: "کاربر"
    val userEmail = sessionManager.getUserEmail() ?: "ایمیل موجود نیست"
    val userId = sessionManager.getUserId() ?: ""

    // تعیین نقش کاربر به فارسی
    val userRoleText = when (userRole) {
        UserRole.ADMIN -> "مدیر کل"
        UserRole.SELLER -> "فروشنده"
        UserRole.CUSTOMER -> "خریدار"
    }

    ModalBottomSheet(
        onDismissRequest = onDismiss,
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "حساب کاربری",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            UserProfileItem(
                label = "نام کاربری",
                value = userName,
                icon = Icons.Default.Person
            )

            UserProfileItem(
                label = "ایمیل",
                value = userEmail,
                icon = Icons.Default.Email
            )

            UserProfileItem(
                label = "نقش کاربری",
                value = userRoleText,
                icon = Icons.Default.Badge
            )

            UserProfileItem(
                label = "شناسه کاربری",
                value = userId.take(8) + "...",
                icon = Icons.Default.Key
            )

            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

/**
 * آیتم اطلاعات کاربری
 */
@Composable
fun UserProfileItem(
    label: String,
    value: String,
    icon: ImageVector,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            modifier = Modifier.size(24.dp),
            tint = SharenShopColors.IconPrimary
        )

        Column(
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray
            )

            Text(
                text = value,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * منوی همبرگری
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HamburgerMenu(
    onDismiss: () -> Unit,
    onNavigateToVariables: () -> Unit,
    onNavigateToSellerCustomers: (String) -> Unit = {},
    onNavigateToPaymentManagement: (String) -> Unit = {},
    onLanguageClick: () -> Unit = {},
    modifier: Modifier = Modifier,
    sessionManager: SessionManager = hiltViewModel<MainViewModel>().sessionManager
) {
    // دریافت شناسه کاربر فعلی
    val currentUserId = sessionManager.getUserId() ?: ""
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "منو",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            HamburgerMenuItem(
                title = "مشتریان",
                icon = Icons.Default.People,
                onClick = {
                    onNavigateToSellerCustomers(currentUserId)
                }
            )

            HamburgerMenuItem(
                title = "پرداخت‌ها",
                icon = Icons.Default.Payment,
                onClick = {
                    onNavigateToPaymentManagement(currentUserId)
                }
            )

            HamburgerMenuItem(
                title = "متغیرها",
                icon = Icons.Default.Settings,
                onClick = onNavigateToVariables
            )

            HamburgerMenuItem(
                title = "تغییر زبان",
                icon = Icons.Default.Language,
                onClick = {
                    onDismiss()
                    onLanguageClick()
                }
            )

            HamburgerMenuItem(
                title = "شبکه‌های اجتماعی",
                icon = Icons.Default.Share,
                onClick = { /* Handle social media */ }
            )

            HamburgerMenuItem(
                title = "درباره ما",
                icon = Icons.Default.Info,
                onClick = { /* Handle about us */ }
            )

            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

/**
 * آیتم منوی همبرگری
 */
@Composable
fun HamburgerMenuItem(
    title: String,
    icon: ImageVector,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            modifier = Modifier.size(24.dp),
            tint = SharenShopColors.IconPrimary
        )

        Text(
            text = title,
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.weight(1f))

        Icon(
            Icons.Default.ChevronRight,
            contentDescription = null,
            modifier = Modifier.size(20.dp),
            tint = Color.Gray
        )
    }
}





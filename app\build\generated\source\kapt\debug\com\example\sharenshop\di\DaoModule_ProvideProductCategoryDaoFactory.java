// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.local.dao.ProductCategoryDao;
import com.example.sharenshop.data.local.database.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaoModule_ProvideProductCategoryDaoFactory implements Factory<ProductCategoryDao> {
  private final Provider<AppDatabase> appDatabaseProvider;

  public DaoModule_ProvideProductCategoryDaoFactory(Provider<AppDatabase> appDatabaseProvider) {
    this.appDatabaseProvider = appDatabaseProvider;
  }

  @Override
  public ProductCategoryDao get() {
    return provideProductCategoryDao(appDatabaseProvider.get());
  }

  public static DaoModule_ProvideProductCategoryDaoFactory create(
      Provider<AppDatabase> appDatabaseProvider) {
    return new DaoModule_ProvideProductCategoryDaoFactory(appDatabaseProvider);
  }

  public static ProductCategoryDao provideProductCategoryDao(AppDatabase appDatabase) {
    return Preconditions.checkNotNullFromProvides(DaoModule.INSTANCE.provideProductCategoryDao(appDatabase));
  }
}

// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.StatisticsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetDashboardStatistics_Factory implements Factory<GetDashboardStatistics> {
  private final Provider<StatisticsRepository> repositoryProvider;

  public GetDashboardStatistics_Factory(Provider<StatisticsRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public GetDashboardStatistics get() {
    return newInstance(repositoryProvider.get());
  }

  public static GetDashboardStatistics_Factory create(
      Provider<StatisticsRepository> repositoryProvider) {
    return new GetDashboardStatistics_Factory(repositoryProvider);
  }

  public static GetDashboardStatistics newInstance(StatisticsRepository repository) {
    return new GetDashboardStatistics(repository);
  }
}

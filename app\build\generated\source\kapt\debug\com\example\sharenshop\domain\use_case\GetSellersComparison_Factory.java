// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.StatisticsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetSellersComparison_Factory implements Factory<GetSellersComparison> {
  private final Provider<StatisticsRepository> repositoryProvider;

  public GetSellersComparison_Factory(Provider<StatisticsRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public GetSellersComparison get() {
    return newInstance(repositoryProvider.get());
  }

  public static GetSellersComparison_Factory create(
      Provider<StatisticsRepository> repositoryProvider) {
    return new GetSellersComparison_Factory(repositoryProvider);
  }

  public static GetSellersComparison newInstance(StatisticsRepository repository) {
    return new GetSellersComparison(repository);
  }
}

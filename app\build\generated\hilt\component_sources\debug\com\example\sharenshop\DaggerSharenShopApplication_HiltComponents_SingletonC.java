// Generated by Da<PERSON> (https://dagger.dev).
package com.example.sharenshop;

import android.app.Activity;
import android.app.Service;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.example.sharenshop.data.database.SharenShopDatabase;
import com.example.sharenshop.data.database.dao.ColorDao;
import com.example.sharenshop.data.database.dao.InventoryTransactionDao;
import com.example.sharenshop.data.database.dao.ProductVariantDao;
import com.example.sharenshop.data.database.dao.SizeDao;
import com.example.sharenshop.data.language.LanguageManager;
import com.example.sharenshop.data.language.LanguageManager_HiltModules_KeyModule_ProvideFactory;
import com.example.sharenshop.data.local.SessionManager;
import com.example.sharenshop.data.local.dao.CartDao;
import com.example.sharenshop.data.local.dao.CustomerApprovalDao;
import com.example.sharenshop.data.local.dao.CustomerDao;
import com.example.sharenshop.data.local.dao.InvoiceDao;
import com.example.sharenshop.data.local.dao.InvoiceItemDao;
import com.example.sharenshop.data.local.dao.NotificationDao;
import com.example.sharenshop.data.local.dao.ProductCategoryDao;
import com.example.sharenshop.data.local.dao.ProductDao;
import com.example.sharenshop.data.local.dao.SellerDao;
import com.example.sharenshop.data.local.dao.UserDao;
import com.example.sharenshop.data.local.database.AppDatabase;
import com.example.sharenshop.data.repository.CartRepositoryImpl;
import com.example.sharenshop.data.repository.CustomerApprovalRepositoryImpl;
import com.example.sharenshop.data.repository.CustomerRepositoryImpl;
import com.example.sharenshop.data.repository.InvoiceRepositoryImpl;
import com.example.sharenshop.data.repository.NotificationRepositoryImpl;
import com.example.sharenshop.data.repository.ProductRepository;
import com.example.sharenshop.data.repository.ProductRepositoryImpl;
import com.example.sharenshop.data.repository.SellerRepositoryImpl;
import com.example.sharenshop.data.repository.SharenShopRepositoryImpl;
import com.example.sharenshop.data.repository.StatisticsRepositoryImpl;
import com.example.sharenshop.data.repository.UserRepositoryImpl;
import com.example.sharenshop.di.DaoModule;
import com.example.sharenshop.di.DaoModule_ProvideCartDaoFactory;
import com.example.sharenshop.di.DaoModule_ProvideCustomerApprovalDaoFactory;
import com.example.sharenshop.di.DaoModule_ProvideCustomerDaoFactory;
import com.example.sharenshop.di.DaoModule_ProvideInvoiceDaoFactory;
import com.example.sharenshop.di.DaoModule_ProvideInvoiceItemDaoFactory;
import com.example.sharenshop.di.DaoModule_ProvideNotificationDaoFactory;
import com.example.sharenshop.di.DaoModule_ProvideProductCategoryDaoFactory;
import com.example.sharenshop.di.DaoModule_ProvideProductDaoFactory;
import com.example.sharenshop.di.DaoModule_ProvideSellerDaoFactory;
import com.example.sharenshop.di.DaoModule_ProvideUserDaoFactory;
import com.example.sharenshop.di.DatabaseModule;
import com.example.sharenshop.di.DatabaseModule_ProvideAppDatabaseFactory;
import com.example.sharenshop.di.DatabaseModule_ProvideColorDaoFactory;
import com.example.sharenshop.di.DatabaseModule_ProvideInventoryTransactionDaoFactory;
import com.example.sharenshop.di.DatabaseModule_ProvideProductDaoFactory;
import com.example.sharenshop.di.DatabaseModule_ProvideProductVariantDaoFactory;
import com.example.sharenshop.di.DatabaseModule_ProvideSharenShopDatabaseFactory;
import com.example.sharenshop.di.DatabaseModule_ProvideSizeDaoFactory;
import com.example.sharenshop.di.NetworkModule;
import com.example.sharenshop.di.NetworkModule_ProvideSupabaseClientFactory;
import com.example.sharenshop.di.SupabaseModule;
import com.example.sharenshop.di.UseCaseModule;
import com.example.sharenshop.di.UseCaseModule_ProvideCartUseCasesFactory;
import com.example.sharenshop.di.UseCaseModule_ProvideCustomerApprovalUseCasesFactory;
import com.example.sharenshop.di.UseCaseModule_ProvideInvoiceUseCasesFactory;
import com.example.sharenshop.di.UseCaseModule_ProvideProductUseCasesFactory;
import com.example.sharenshop.di.UseCaseModule_ProvideSellerUseCasesFactory;
import com.example.sharenshop.di.UseCaseModule_ProvideStatisticsUseCasesFactory;
import com.example.sharenshop.di.UseCaseModule_ProvideUserUseCasesFactory;
import com.example.sharenshop.domain.repository.CartRepository;
import com.example.sharenshop.domain.repository.CustomerApprovalRepository;
import com.example.sharenshop.domain.repository.CustomerRepository;
import com.example.sharenshop.domain.repository.InvoiceRepository;
import com.example.sharenshop.domain.repository.SellerRepository;
import com.example.sharenshop.domain.repository.UserRepository;
import com.example.sharenshop.domain.use_case.CartUseCases;
import com.example.sharenshop.domain.use_case.CustomerApprovalUseCases;
import com.example.sharenshop.domain.use_case.InvoiceUseCases;
import com.example.sharenshop.domain.use_case.ProductUseCases;
import com.example.sharenshop.domain.use_case.SellerUseCases;
import com.example.sharenshop.domain.use_case.StatisticsUseCases;
import com.example.sharenshop.domain.use_case.UserUseCases;
import com.example.sharenshop.presentation.addproduct.AddProductViewModel;
import com.example.sharenshop.presentation.addproduct.AddProductViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.sharenshop.presentation.auth.AuthViewModel;
import com.example.sharenshop.presentation.auth.AuthViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.sharenshop.presentation.cart.CartViewModel;
import com.example.sharenshop.presentation.cart.CartViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.sharenshop.presentation.customer.CustomerViewModel;
import com.example.sharenshop.presentation.customer.CustomerViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.sharenshop.presentation.customer_approval.CustomerApprovalViewModel;
import com.example.sharenshop.presentation.customer_approval.CustomerApprovalViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.sharenshop.presentation.home.HomeViewModel;
import com.example.sharenshop.presentation.home.HomeViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.sharenshop.presentation.invoice.InvoiceViewModel;
import com.example.sharenshop.presentation.invoice.InvoiceViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.sharenshop.presentation.main.MainViewModel;
import com.example.sharenshop.presentation.main.MainViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.sharenshop.presentation.notification.NotificationViewModel;
import com.example.sharenshop.presentation.notification.NotificationViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.sharenshop.presentation.product.ProductViewModel;
import com.example.sharenshop.presentation.product.ProductViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.sharenshop.presentation.seller.SellerViewModel;
import com.example.sharenshop.presentation.seller.SellerViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.sharenshop.presentation.statistics.StatisticsViewModel;
import com.example.sharenshop.presentation.statistics.StatisticsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.sharenshop.presentation.user_management.UserManagementViewModel;
import com.example.sharenshop.presentation.user_management.UserManagementViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.sharenshop.presentation.variables.VariableViewModel;
import com.example.sharenshop.presentation.variables.VariableViewModel_HiltModules_KeyModule_ProvideFactory;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.flags.HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.managers.SavedStateHandleHolder;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.MapBuilder;
import dagger.internal.Preconditions;
import dagger.internal.SetBuilder;
import io.github.jan.supabase.SupabaseClient;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import javax.inject.Provider;

@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaggerSharenShopApplication_HiltComponents_SingletonC {
  private DaggerSharenShopApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder daoModule(DaoModule daoModule) {
      Preconditions.checkNotNull(daoModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder databaseModule(DatabaseModule databaseModule) {
      Preconditions.checkNotNull(databaseModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule(
        HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule) {
      Preconditions.checkNotNull(hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder networkModule(NetworkModule networkModule) {
      Preconditions.checkNotNull(networkModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder supabaseModule(SupabaseModule supabaseModule) {
      Preconditions.checkNotNull(supabaseModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder useCaseModule(UseCaseModule useCaseModule) {
      Preconditions.checkNotNull(useCaseModule);
      return this;
    }

    public SharenShopApplication_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements SharenShopApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private SavedStateHandleHolder savedStateHandleHolder;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ActivityRetainedCBuilder savedStateHandleHolder(
        SavedStateHandleHolder savedStateHandleHolder) {
      this.savedStateHandleHolder = Preconditions.checkNotNull(savedStateHandleHolder);
      return this;
    }

    @Override
    public SharenShopApplication_HiltComponents.ActivityRetainedC build() {
      Preconditions.checkBuilderRequirement(savedStateHandleHolder, SavedStateHandleHolder.class);
      return new ActivityRetainedCImpl(singletonCImpl, savedStateHandleHolder);
    }
  }

  private static final class ActivityCBuilder implements SharenShopApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public SharenShopApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements SharenShopApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public SharenShopApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements SharenShopApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public SharenShopApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements SharenShopApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public SharenShopApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements SharenShopApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public SharenShopApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements SharenShopApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public SharenShopApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends SharenShopApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends SharenShopApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }
  }

  private static final class ViewCImpl extends SharenShopApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends SharenShopApplication_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectMainActivity(MainActivity mainActivity) {
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Set<String> getViewModelKeys() {
      return SetBuilder.<String>newSetBuilder(15).add(AddProductViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(AuthViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(CartViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(CustomerApprovalViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(CustomerViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(HomeViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(InvoiceViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(LanguageManager_HiltModules_KeyModule_ProvideFactory.provide()).add(MainViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(NotificationViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(ProductViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(SellerViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(StatisticsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(UserManagementViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(VariableViewModel_HiltModules_KeyModule_ProvideFactory.provide()).build();
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }
  }

  private static final class ViewModelCImpl extends SharenShopApplication_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<AddProductViewModel> addProductViewModelProvider;

    private Provider<AuthViewModel> authViewModelProvider;

    private Provider<CartViewModel> cartViewModelProvider;

    private Provider<CustomerApprovalViewModel> customerApprovalViewModelProvider;

    private Provider<CustomerViewModel> customerViewModelProvider;

    private Provider<HomeViewModel> homeViewModelProvider;

    private Provider<InvoiceViewModel> invoiceViewModelProvider;

    private Provider<LanguageManager> languageManagerProvider;

    private Provider<MainViewModel> mainViewModelProvider;

    private Provider<NotificationViewModel> notificationViewModelProvider;

    private Provider<ProductViewModel> productViewModelProvider;

    private Provider<SellerViewModel> sellerViewModelProvider;

    private Provider<StatisticsViewModel> statisticsViewModelProvider;

    private Provider<UserManagementViewModel> userManagementViewModelProvider;

    private Provider<VariableViewModel> variableViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;

      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.addProductViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.authViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.cartViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.customerApprovalViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.customerViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
      this.homeViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 5);
      this.invoiceViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 6);
      this.languageManagerProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 7);
      this.mainViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 8);
      this.notificationViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 9);
      this.productViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 10);
      this.sellerViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 11);
      this.statisticsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 12);
      this.userManagementViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 13);
      this.variableViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 14);
    }

    @Override
    public Map<String, Provider<ViewModel>> getHiltViewModelMap() {
      return MapBuilder.<String, Provider<ViewModel>>newMapBuilder(15).put("com.example.sharenshop.presentation.addproduct.AddProductViewModel", ((Provider) addProductViewModelProvider)).put("com.example.sharenshop.presentation.auth.AuthViewModel", ((Provider) authViewModelProvider)).put("com.example.sharenshop.presentation.cart.CartViewModel", ((Provider) cartViewModelProvider)).put("com.example.sharenshop.presentation.customer_approval.CustomerApprovalViewModel", ((Provider) customerApprovalViewModelProvider)).put("com.example.sharenshop.presentation.customer.CustomerViewModel", ((Provider) customerViewModelProvider)).put("com.example.sharenshop.presentation.home.HomeViewModel", ((Provider) homeViewModelProvider)).put("com.example.sharenshop.presentation.invoice.InvoiceViewModel", ((Provider) invoiceViewModelProvider)).put("com.example.sharenshop.data.language.LanguageManager", ((Provider) languageManagerProvider)).put("com.example.sharenshop.presentation.main.MainViewModel", ((Provider) mainViewModelProvider)).put("com.example.sharenshop.presentation.notification.NotificationViewModel", ((Provider) notificationViewModelProvider)).put("com.example.sharenshop.presentation.product.ProductViewModel", ((Provider) productViewModelProvider)).put("com.example.sharenshop.presentation.seller.SellerViewModel", ((Provider) sellerViewModelProvider)).put("com.example.sharenshop.presentation.statistics.StatisticsViewModel", ((Provider) statisticsViewModelProvider)).put("com.example.sharenshop.presentation.user_management.UserManagementViewModel", ((Provider) userManagementViewModelProvider)).put("com.example.sharenshop.presentation.variables.VariableViewModel", ((Provider) variableViewModelProvider)).build();
    }

    @Override
    public Map<String, Object> getHiltViewModelAssistedMap() {
      return Collections.<String, Object>emptyMap();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.example.sharenshop.presentation.addproduct.AddProductViewModel 
          return (T) new AddProductViewModel(singletonCImpl.productRepositoryProvider.get());

          case 1: // com.example.sharenshop.presentation.auth.AuthViewModel 
          return (T) new AuthViewModel(singletonCImpl.provideUserUseCasesProvider.get(), singletonCImpl.provideCustomerApprovalUseCasesProvider.get(), singletonCImpl.sessionManagerProvider.get());

          case 2: // com.example.sharenshop.presentation.cart.CartViewModel 
          return (T) new CartViewModel(singletonCImpl.provideCartUseCasesProvider.get(), singletonCImpl.provideSupabaseClientProvider.get());

          case 3: // com.example.sharenshop.presentation.customer_approval.CustomerApprovalViewModel 
          return (T) new CustomerApprovalViewModel(singletonCImpl.provideCustomerApprovalUseCasesProvider.get());

          case 4: // com.example.sharenshop.presentation.customer.CustomerViewModel 
          return (T) new CustomerViewModel(singletonCImpl.bindCustomerRepositoryProvider.get());

          case 5: // com.example.sharenshop.presentation.home.HomeViewModel 
          return (T) new HomeViewModel(singletonCImpl.provideStatisticsUseCasesProvider.get());

          case 6: // com.example.sharenshop.presentation.invoice.InvoiceViewModel 
          return (T) new InvoiceViewModel(singletonCImpl.provideInvoiceUseCasesProvider.get());

          case 7: // com.example.sharenshop.data.language.LanguageManager 
          return (T) new LanguageManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 8: // com.example.sharenshop.presentation.main.MainViewModel 
          return (T) new MainViewModel(singletonCImpl.sharenShopRepositoryImplProvider.get());

          case 9: // com.example.sharenshop.presentation.notification.NotificationViewModel 
          return (T) new NotificationViewModel(singletonCImpl.notificationRepositoryImplProvider.get(), singletonCImpl.bindUserRepositoryProvider.get());

          case 10: // com.example.sharenshop.presentation.product.ProductViewModel 
          return (T) new ProductViewModel(singletonCImpl.provideProductUseCasesProvider.get());

          case 11: // com.example.sharenshop.presentation.seller.SellerViewModel 
          return (T) new SellerViewModel(singletonCImpl.provideSellerUseCasesProvider.get());

          case 12: // com.example.sharenshop.presentation.statistics.StatisticsViewModel 
          return (T) new StatisticsViewModel(singletonCImpl.provideStatisticsUseCasesProvider.get());

          case 13: // com.example.sharenshop.presentation.user_management.UserManagementViewModel 
          return (T) new UserManagementViewModel(singletonCImpl.provideUserUseCasesProvider.get());

          case 14: // com.example.sharenshop.presentation.variables.VariableViewModel 
          return (T) new VariableViewModel(singletonCImpl.productRepositoryProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends SharenShopApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl,
        SavedStateHandleHolder savedStateHandleHolderParam) {
      this.singletonCImpl = singletonCImpl;

      initialize(savedStateHandleHolderParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandleHolder savedStateHandleHolderParam) {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends SharenShopApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }
  }

  private static final class SingletonCImpl extends SharenShopApplication_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<SharenShopDatabase> provideSharenShopDatabaseProvider;

    private Provider<ProductRepository> productRepositoryProvider;

    private Provider<AppDatabase> provideAppDatabaseProvider;

    private Provider<UserDao> provideUserDaoProvider;

    private Provider<SupabaseClient> provideSupabaseClientProvider;

    private Provider<UserRepositoryImpl> userRepositoryImplProvider;

    private Provider<UserRepository> bindUserRepositoryProvider;

    private Provider<UserUseCases> provideUserUseCasesProvider;

    private Provider<CustomerApprovalDao> provideCustomerApprovalDaoProvider;

    private Provider<CustomerApprovalRepositoryImpl> customerApprovalRepositoryImplProvider;

    private Provider<CustomerApprovalRepository> bindCustomerApprovalRepositoryProvider;

    private Provider<NotificationDao> provideNotificationDaoProvider;

    private Provider<NotificationRepositoryImpl> notificationRepositoryImplProvider;

    private Provider<CustomerApprovalUseCases> provideCustomerApprovalUseCasesProvider;

    private Provider<SessionManager> sessionManagerProvider;

    private Provider<CartDao> provideCartDaoProvider;

    private Provider<ProductDao> provideProductDaoProvider;

    private Provider<CartRepositoryImpl> cartRepositoryImplProvider;

    private Provider<CartRepository> bindCartRepositoryProvider;

    private Provider<CartUseCases> provideCartUseCasesProvider;

    private Provider<CustomerDao> provideCustomerDaoProvider;

    private Provider<CustomerRepositoryImpl> customerRepositoryImplProvider;

    private Provider<CustomerRepository> bindCustomerRepositoryProvider;

    private Provider<InvoiceDao> provideInvoiceDaoProvider;

    private Provider<InvoiceItemDao> provideInvoiceItemDaoProvider;

    private Provider<InvoiceRepositoryImpl> invoiceRepositoryImplProvider;

    private Provider<InvoiceRepository> bindInvoiceRepositoryProvider;

    private Provider<StatisticsRepositoryImpl> statisticsRepositoryImplProvider;

    private Provider<StatisticsUseCases> provideStatisticsUseCasesProvider;

    private Provider<InvoiceUseCases> provideInvoiceUseCasesProvider;

    private Provider<SharenShopRepositoryImpl> sharenShopRepositoryImplProvider;

    private Provider<ProductCategoryDao> provideProductCategoryDaoProvider;

    private Provider<ProductRepositoryImpl> productRepositoryImplProvider;

    private Provider<com.example.sharenshop.domain.repository.ProductRepository> bindProductRepositoryProvider;

    private Provider<ProductUseCases> provideProductUseCasesProvider;

    private Provider<SellerDao> provideSellerDaoProvider;

    private Provider<SellerRepositoryImpl> sellerRepositoryImplProvider;

    private Provider<SellerRepository> bindSellerRepositoryProvider;

    private Provider<SellerUseCases> provideSellerUseCasesProvider;

    private SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);

    }

    private com.example.sharenshop.data.database.dao.ProductDao productDao() {
      return DatabaseModule_ProvideProductDaoFactory.provideProductDao(provideSharenShopDatabaseProvider.get());
    }

    private ProductVariantDao productVariantDao() {
      return DatabaseModule_ProvideProductVariantDaoFactory.provideProductVariantDao(provideSharenShopDatabaseProvider.get());
    }

    private ColorDao colorDao() {
      return DatabaseModule_ProvideColorDaoFactory.provideColorDao(provideSharenShopDatabaseProvider.get());
    }

    private SizeDao sizeDao() {
      return DatabaseModule_ProvideSizeDaoFactory.provideSizeDao(provideSharenShopDatabaseProvider.get());
    }

    private InventoryTransactionDao inventoryTransactionDao() {
      return DatabaseModule_ProvideInventoryTransactionDaoFactory.provideInventoryTransactionDao(provideSharenShopDatabaseProvider.get());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.provideSharenShopDatabaseProvider = DoubleCheck.provider(new SwitchingProvider<SharenShopDatabase>(singletonCImpl, 0));
      this.productRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<ProductRepository>(singletonCImpl, 1));
      this.provideAppDatabaseProvider = DoubleCheck.provider(new SwitchingProvider<AppDatabase>(singletonCImpl, 5));
      this.provideUserDaoProvider = DoubleCheck.provider(new SwitchingProvider<UserDao>(singletonCImpl, 4));
      this.provideSupabaseClientProvider = DoubleCheck.provider(new SwitchingProvider<SupabaseClient>(singletonCImpl, 6));
      this.userRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 3);
      this.bindUserRepositoryProvider = DoubleCheck.provider((Provider) userRepositoryImplProvider);
      this.provideUserUseCasesProvider = DoubleCheck.provider(new SwitchingProvider<UserUseCases>(singletonCImpl, 2));
      this.provideCustomerApprovalDaoProvider = DoubleCheck.provider(new SwitchingProvider<CustomerApprovalDao>(singletonCImpl, 9));
      this.customerApprovalRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 8);
      this.bindCustomerApprovalRepositoryProvider = DoubleCheck.provider((Provider) customerApprovalRepositoryImplProvider);
      this.provideNotificationDaoProvider = DoubleCheck.provider(new SwitchingProvider<NotificationDao>(singletonCImpl, 11));
      this.notificationRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<NotificationRepositoryImpl>(singletonCImpl, 10));
      this.provideCustomerApprovalUseCasesProvider = DoubleCheck.provider(new SwitchingProvider<CustomerApprovalUseCases>(singletonCImpl, 7));
      this.sessionManagerProvider = DoubleCheck.provider(new SwitchingProvider<SessionManager>(singletonCImpl, 12));
      this.provideCartDaoProvider = DoubleCheck.provider(new SwitchingProvider<CartDao>(singletonCImpl, 15));
      this.provideProductDaoProvider = DoubleCheck.provider(new SwitchingProvider<ProductDao>(singletonCImpl, 16));
      this.cartRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 14);
      this.bindCartRepositoryProvider = DoubleCheck.provider((Provider) cartRepositoryImplProvider);
      this.provideCartUseCasesProvider = DoubleCheck.provider(new SwitchingProvider<CartUseCases>(singletonCImpl, 13));
      this.provideCustomerDaoProvider = DoubleCheck.provider(new SwitchingProvider<CustomerDao>(singletonCImpl, 18));
      this.customerRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 17);
      this.bindCustomerRepositoryProvider = DoubleCheck.provider((Provider) customerRepositoryImplProvider);
      this.provideInvoiceDaoProvider = DoubleCheck.provider(new SwitchingProvider<InvoiceDao>(singletonCImpl, 22));
      this.provideInvoiceItemDaoProvider = DoubleCheck.provider(new SwitchingProvider<InvoiceItemDao>(singletonCImpl, 23));
      this.invoiceRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 21);
      this.bindInvoiceRepositoryProvider = DoubleCheck.provider((Provider) invoiceRepositoryImplProvider);
      this.statisticsRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<StatisticsRepositoryImpl>(singletonCImpl, 20));
      this.provideStatisticsUseCasesProvider = DoubleCheck.provider(new SwitchingProvider<StatisticsUseCases>(singletonCImpl, 19));
      this.provideInvoiceUseCasesProvider = DoubleCheck.provider(new SwitchingProvider<InvoiceUseCases>(singletonCImpl, 24));
      this.sharenShopRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<SharenShopRepositoryImpl>(singletonCImpl, 25));
      this.provideProductCategoryDaoProvider = DoubleCheck.provider(new SwitchingProvider<ProductCategoryDao>(singletonCImpl, 28));
      this.productRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 27);
      this.bindProductRepositoryProvider = DoubleCheck.provider((Provider) productRepositoryImplProvider);
      this.provideProductUseCasesProvider = DoubleCheck.provider(new SwitchingProvider<ProductUseCases>(singletonCImpl, 26));
      this.provideSellerDaoProvider = DoubleCheck.provider(new SwitchingProvider<SellerDao>(singletonCImpl, 31));
      this.sellerRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 30);
      this.bindSellerRepositoryProvider = DoubleCheck.provider((Provider) sellerRepositoryImplProvider);
      this.provideSellerUseCasesProvider = DoubleCheck.provider(new SwitchingProvider<SellerUseCases>(singletonCImpl, 29));
    }

    @Override
    public void injectSharenShopApplication(SharenShopApplication sharenShopApplication) {
      injectSharenShopApplication2(sharenShopApplication);
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return Collections.<Boolean>emptySet();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    private SharenShopApplication injectSharenShopApplication2(SharenShopApplication instance) {
      SharenShopApplication_MembersInjector.injectDatabase(instance, provideSharenShopDatabaseProvider.get());
      return instance;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.example.sharenshop.data.database.SharenShopDatabase 
          return (T) DatabaseModule_ProvideSharenShopDatabaseFactory.provideSharenShopDatabase(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 1: // com.example.sharenshop.data.repository.ProductRepository 
          return (T) new ProductRepository(singletonCImpl.productDao(), singletonCImpl.productVariantDao(), singletonCImpl.colorDao(), singletonCImpl.sizeDao(), singletonCImpl.inventoryTransactionDao());

          case 2: // com.example.sharenshop.domain.use_case.UserUseCases 
          return (T) UseCaseModule_ProvideUserUseCasesFactory.provideUserUseCases(singletonCImpl.bindUserRepositoryProvider.get());

          case 3: // com.example.sharenshop.data.repository.UserRepositoryImpl 
          return (T) new UserRepositoryImpl(singletonCImpl.provideUserDaoProvider.get(), singletonCImpl.provideSupabaseClientProvider.get());

          case 4: // com.example.sharenshop.data.local.dao.UserDao 
          return (T) DaoModule_ProvideUserDaoFactory.provideUserDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 5: // com.example.sharenshop.data.local.database.AppDatabase 
          return (T) DatabaseModule_ProvideAppDatabaseFactory.provideAppDatabase(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 6: // io.github.jan.supabase.SupabaseClient 
          return (T) NetworkModule_ProvideSupabaseClientFactory.provideSupabaseClient();

          case 7: // com.example.sharenshop.domain.use_case.CustomerApprovalUseCases 
          return (T) UseCaseModule_ProvideCustomerApprovalUseCasesFactory.provideCustomerApprovalUseCases(singletonCImpl.bindCustomerApprovalRepositoryProvider.get(), singletonCImpl.bindUserRepositoryProvider.get(), singletonCImpl.notificationRepositoryImplProvider.get());

          case 8: // com.example.sharenshop.data.repository.CustomerApprovalRepositoryImpl 
          return (T) new CustomerApprovalRepositoryImpl(singletonCImpl.provideCustomerApprovalDaoProvider.get());

          case 9: // com.example.sharenshop.data.local.dao.CustomerApprovalDao 
          return (T) DaoModule_ProvideCustomerApprovalDaoFactory.provideCustomerApprovalDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 10: // com.example.sharenshop.data.repository.NotificationRepositoryImpl 
          return (T) new NotificationRepositoryImpl(singletonCImpl.provideNotificationDaoProvider.get());

          case 11: // com.example.sharenshop.data.local.dao.NotificationDao 
          return (T) DaoModule_ProvideNotificationDaoFactory.provideNotificationDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 12: // com.example.sharenshop.data.local.SessionManager 
          return (T) new SessionManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 13: // com.example.sharenshop.domain.use_case.CartUseCases 
          return (T) UseCaseModule_ProvideCartUseCasesFactory.provideCartUseCases(singletonCImpl.bindCartRepositoryProvider.get());

          case 14: // com.example.sharenshop.data.repository.CartRepositoryImpl 
          return (T) new CartRepositoryImpl(singletonCImpl.provideCartDaoProvider.get(), singletonCImpl.provideProductDaoProvider.get(), singletonCImpl.provideSupabaseClientProvider.get());

          case 15: // com.example.sharenshop.data.local.dao.CartDao 
          return (T) DaoModule_ProvideCartDaoFactory.provideCartDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 16: // com.example.sharenshop.data.local.dao.ProductDao 
          return (T) DaoModule_ProvideProductDaoFactory.provideProductDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 17: // com.example.sharenshop.data.repository.CustomerRepositoryImpl 
          return (T) new CustomerRepositoryImpl(singletonCImpl.provideCustomerDaoProvider.get());

          case 18: // com.example.sharenshop.data.local.dao.CustomerDao 
          return (T) DaoModule_ProvideCustomerDaoFactory.provideCustomerDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 19: // com.example.sharenshop.domain.use_case.StatisticsUseCases 
          return (T) UseCaseModule_ProvideStatisticsUseCasesFactory.provideStatisticsUseCases(singletonCImpl.statisticsRepositoryImplProvider.get());

          case 20: // com.example.sharenshop.data.repository.StatisticsRepositoryImpl 
          return (T) new StatisticsRepositoryImpl(singletonCImpl.bindInvoiceRepositoryProvider.get(), singletonCImpl.productRepositoryProvider.get(), singletonCImpl.bindUserRepositoryProvider.get());

          case 21: // com.example.sharenshop.data.repository.InvoiceRepositoryImpl 
          return (T) new InvoiceRepositoryImpl(singletonCImpl.provideInvoiceDaoProvider.get(), singletonCImpl.provideInvoiceItemDaoProvider.get(), singletonCImpl.provideSupabaseClientProvider.get());

          case 22: // com.example.sharenshop.data.local.dao.InvoiceDao 
          return (T) DaoModule_ProvideInvoiceDaoFactory.provideInvoiceDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 23: // com.example.sharenshop.data.local.dao.InvoiceItemDao 
          return (T) DaoModule_ProvideInvoiceItemDaoFactory.provideInvoiceItemDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 24: // com.example.sharenshop.domain.use_case.InvoiceUseCases 
          return (T) UseCaseModule_ProvideInvoiceUseCasesFactory.provideInvoiceUseCases(singletonCImpl.bindInvoiceRepositoryProvider.get());

          case 25: // com.example.sharenshop.data.repository.SharenShopRepositoryImpl 
          return (T) new SharenShopRepositoryImpl(singletonCImpl.statisticsRepositoryImplProvider.get(), singletonCImpl.notificationRepositoryImplProvider.get());

          case 26: // com.example.sharenshop.domain.use_case.ProductUseCases 
          return (T) UseCaseModule_ProvideProductUseCasesFactory.provideProductUseCases(singletonCImpl.bindProductRepositoryProvider.get());

          case 27: // com.example.sharenshop.data.repository.ProductRepositoryImpl 
          return (T) new ProductRepositoryImpl(singletonCImpl.provideProductDaoProvider.get(), singletonCImpl.provideProductCategoryDaoProvider.get(), singletonCImpl.provideSupabaseClientProvider.get(), ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 28: // com.example.sharenshop.data.local.dao.ProductCategoryDao 
          return (T) DaoModule_ProvideProductCategoryDaoFactory.provideProductCategoryDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 29: // com.example.sharenshop.domain.use_case.SellerUseCases 
          return (T) UseCaseModule_ProvideSellerUseCasesFactory.provideSellerUseCases(singletonCImpl.bindSellerRepositoryProvider.get());

          case 30: // com.example.sharenshop.data.repository.SellerRepositoryImpl 
          return (T) new SellerRepositoryImpl(singletonCImpl.provideSellerDaoProvider.get());

          case 31: // com.example.sharenshop.data.local.dao.SellerDao 
          return (T) DaoModule_ProvideSellerDaoFactory.provideSellerDao(singletonCImpl.provideAppDatabaseProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }
}

package com.example.sharenshop.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.sharenshop.data.model.ApprovalStatus;
import com.example.sharenshop.data.model.CustomerApprovalRequest;
import java.lang.Class;
import java.lang.Exception;
import java.lang.IllegalArgumentException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class CustomerApprovalDao_Impl implements CustomerApprovalDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<CustomerApprovalRequest> __insertionAdapterOfCustomerApprovalRequest;

  private final EntityDeletionOrUpdateAdapter<CustomerApprovalRequest> __deletionAdapterOfCustomerApprovalRequest;

  private final EntityDeletionOrUpdateAdapter<CustomerApprovalRequest> __updateAdapterOfCustomerApprovalRequest;

  private final SharedSQLiteStatement __preparedStmtOfUpdateRequestStatus;

  private final SharedSQLiteStatement __preparedStmtOfDeleteExpiredRequests;

  public CustomerApprovalDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfCustomerApprovalRequest = new EntityInsertionAdapter<CustomerApprovalRequest>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `customer_approval_requests` (`id`,`customerEmail`,`customerName`,`customerPhone`,`referrerCode`,`referrerId`,`status`,`requestDate`,`responseDate`,`notes`) VALUES (?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CustomerApprovalRequest entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getCustomerEmail() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCustomerEmail());
        }
        if (entity.getCustomerName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getCustomerName());
        }
        if (entity.getCustomerPhone() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getCustomerPhone());
        }
        if (entity.getReferrerCode() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getReferrerCode());
        }
        if (entity.getReferrerId() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getReferrerId());
        }
        statement.bindString(7, __ApprovalStatus_enumToString(entity.getStatus()));
        statement.bindLong(8, entity.getRequestDate());
        if (entity.getResponseDate() == null) {
          statement.bindNull(9);
        } else {
          statement.bindLong(9, entity.getResponseDate());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getNotes());
        }
      }
    };
    this.__deletionAdapterOfCustomerApprovalRequest = new EntityDeletionOrUpdateAdapter<CustomerApprovalRequest>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `customer_approval_requests` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CustomerApprovalRequest entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
      }
    };
    this.__updateAdapterOfCustomerApprovalRequest = new EntityDeletionOrUpdateAdapter<CustomerApprovalRequest>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `customer_approval_requests` SET `id` = ?,`customerEmail` = ?,`customerName` = ?,`customerPhone` = ?,`referrerCode` = ?,`referrerId` = ?,`status` = ?,`requestDate` = ?,`responseDate` = ?,`notes` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CustomerApprovalRequest entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getCustomerEmail() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCustomerEmail());
        }
        if (entity.getCustomerName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getCustomerName());
        }
        if (entity.getCustomerPhone() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getCustomerPhone());
        }
        if (entity.getReferrerCode() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getReferrerCode());
        }
        if (entity.getReferrerId() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getReferrerId());
        }
        statement.bindString(7, __ApprovalStatus_enumToString(entity.getStatus()));
        statement.bindLong(8, entity.getRequestDate());
        if (entity.getResponseDate() == null) {
          statement.bindNull(9);
        } else {
          statement.bindLong(9, entity.getResponseDate());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getNotes());
        }
        if (entity.getId() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getId());
        }
      }
    };
    this.__preparedStmtOfUpdateRequestStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE customer_approval_requests SET status = ?, responseDate = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteExpiredRequests = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM customer_approval_requests WHERE status = 'expired' OR (status = 'pending' AND requestDate < ?)";
        return _query;
      }
    };
  }

  @Override
  public Object insertRequest(final CustomerApprovalRequest request,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfCustomerApprovalRequest.insert(request);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteRequest(final CustomerApprovalRequest request,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfCustomerApprovalRequest.handle(request);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateRequest(final CustomerApprovalRequest request,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfCustomerApprovalRequest.handle(request);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateRequestStatus(final String requestId, final ApprovalStatus status,
      final long responseDate, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateRequestStatus.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, __ApprovalStatus_enumToString(status));
        _argIndex = 2;
        _stmt.bindLong(_argIndex, responseDate);
        _argIndex = 3;
        if (requestId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, requestId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateRequestStatus.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteExpiredRequests(final long expireDate,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteExpiredRequests.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, expireDate);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteExpiredRequests.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getRequestById(final String id,
      final Continuation<? super CustomerApprovalRequest> $completion) {
    final String _sql = "SELECT * FROM customer_approval_requests WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (id == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, id);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CustomerApprovalRequest>() {
      @Override
      @Nullable
      public CustomerApprovalRequest call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCustomerEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "customerEmail");
          final int _cursorIndexOfCustomerName = CursorUtil.getColumnIndexOrThrow(_cursor, "customerName");
          final int _cursorIndexOfCustomerPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "customerPhone");
          final int _cursorIndexOfReferrerCode = CursorUtil.getColumnIndexOrThrow(_cursor, "referrerCode");
          final int _cursorIndexOfReferrerId = CursorUtil.getColumnIndexOrThrow(_cursor, "referrerId");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfRequestDate = CursorUtil.getColumnIndexOrThrow(_cursor, "requestDate");
          final int _cursorIndexOfResponseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "responseDate");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final CustomerApprovalRequest _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpCustomerEmail;
            if (_cursor.isNull(_cursorIndexOfCustomerEmail)) {
              _tmpCustomerEmail = null;
            } else {
              _tmpCustomerEmail = _cursor.getString(_cursorIndexOfCustomerEmail);
            }
            final String _tmpCustomerName;
            if (_cursor.isNull(_cursorIndexOfCustomerName)) {
              _tmpCustomerName = null;
            } else {
              _tmpCustomerName = _cursor.getString(_cursorIndexOfCustomerName);
            }
            final String _tmpCustomerPhone;
            if (_cursor.isNull(_cursorIndexOfCustomerPhone)) {
              _tmpCustomerPhone = null;
            } else {
              _tmpCustomerPhone = _cursor.getString(_cursorIndexOfCustomerPhone);
            }
            final String _tmpReferrerCode;
            if (_cursor.isNull(_cursorIndexOfReferrerCode)) {
              _tmpReferrerCode = null;
            } else {
              _tmpReferrerCode = _cursor.getString(_cursorIndexOfReferrerCode);
            }
            final String _tmpReferrerId;
            if (_cursor.isNull(_cursorIndexOfReferrerId)) {
              _tmpReferrerId = null;
            } else {
              _tmpReferrerId = _cursor.getString(_cursorIndexOfReferrerId);
            }
            final ApprovalStatus _tmpStatus;
            _tmpStatus = __ApprovalStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final long _tmpRequestDate;
            _tmpRequestDate = _cursor.getLong(_cursorIndexOfRequestDate);
            final Long _tmpResponseDate;
            if (_cursor.isNull(_cursorIndexOfResponseDate)) {
              _tmpResponseDate = null;
            } else {
              _tmpResponseDate = _cursor.getLong(_cursorIndexOfResponseDate);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            _result = new CustomerApprovalRequest(_tmpId,_tmpCustomerEmail,_tmpCustomerName,_tmpCustomerPhone,_tmpReferrerCode,_tmpReferrerId,_tmpStatus,_tmpRequestDate,_tmpResponseDate,_tmpNotes);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CustomerApprovalRequest>> getAllRequests() {
    final String _sql = "SELECT * FROM customer_approval_requests ORDER BY requestDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"customer_approval_requests"}, new Callable<List<CustomerApprovalRequest>>() {
      @Override
      @NonNull
      public List<CustomerApprovalRequest> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCustomerEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "customerEmail");
          final int _cursorIndexOfCustomerName = CursorUtil.getColumnIndexOrThrow(_cursor, "customerName");
          final int _cursorIndexOfCustomerPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "customerPhone");
          final int _cursorIndexOfReferrerCode = CursorUtil.getColumnIndexOrThrow(_cursor, "referrerCode");
          final int _cursorIndexOfReferrerId = CursorUtil.getColumnIndexOrThrow(_cursor, "referrerId");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfRequestDate = CursorUtil.getColumnIndexOrThrow(_cursor, "requestDate");
          final int _cursorIndexOfResponseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "responseDate");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final List<CustomerApprovalRequest> _result = new ArrayList<CustomerApprovalRequest>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CustomerApprovalRequest _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpCustomerEmail;
            if (_cursor.isNull(_cursorIndexOfCustomerEmail)) {
              _tmpCustomerEmail = null;
            } else {
              _tmpCustomerEmail = _cursor.getString(_cursorIndexOfCustomerEmail);
            }
            final String _tmpCustomerName;
            if (_cursor.isNull(_cursorIndexOfCustomerName)) {
              _tmpCustomerName = null;
            } else {
              _tmpCustomerName = _cursor.getString(_cursorIndexOfCustomerName);
            }
            final String _tmpCustomerPhone;
            if (_cursor.isNull(_cursorIndexOfCustomerPhone)) {
              _tmpCustomerPhone = null;
            } else {
              _tmpCustomerPhone = _cursor.getString(_cursorIndexOfCustomerPhone);
            }
            final String _tmpReferrerCode;
            if (_cursor.isNull(_cursorIndexOfReferrerCode)) {
              _tmpReferrerCode = null;
            } else {
              _tmpReferrerCode = _cursor.getString(_cursorIndexOfReferrerCode);
            }
            final String _tmpReferrerId;
            if (_cursor.isNull(_cursorIndexOfReferrerId)) {
              _tmpReferrerId = null;
            } else {
              _tmpReferrerId = _cursor.getString(_cursorIndexOfReferrerId);
            }
            final ApprovalStatus _tmpStatus;
            _tmpStatus = __ApprovalStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final long _tmpRequestDate;
            _tmpRequestDate = _cursor.getLong(_cursorIndexOfRequestDate);
            final Long _tmpResponseDate;
            if (_cursor.isNull(_cursorIndexOfResponseDate)) {
              _tmpResponseDate = null;
            } else {
              _tmpResponseDate = _cursor.getLong(_cursorIndexOfResponseDate);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            _item = new CustomerApprovalRequest(_tmpId,_tmpCustomerEmail,_tmpCustomerName,_tmpCustomerPhone,_tmpReferrerCode,_tmpReferrerId,_tmpStatus,_tmpRequestDate,_tmpResponseDate,_tmpNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CustomerApprovalRequest>> getRequestsByReferrer(final String referrerId) {
    final String _sql = "SELECT * FROM customer_approval_requests WHERE referrerId = ? ORDER BY requestDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (referrerId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, referrerId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"customer_approval_requests"}, new Callable<List<CustomerApprovalRequest>>() {
      @Override
      @NonNull
      public List<CustomerApprovalRequest> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCustomerEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "customerEmail");
          final int _cursorIndexOfCustomerName = CursorUtil.getColumnIndexOrThrow(_cursor, "customerName");
          final int _cursorIndexOfCustomerPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "customerPhone");
          final int _cursorIndexOfReferrerCode = CursorUtil.getColumnIndexOrThrow(_cursor, "referrerCode");
          final int _cursorIndexOfReferrerId = CursorUtil.getColumnIndexOrThrow(_cursor, "referrerId");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfRequestDate = CursorUtil.getColumnIndexOrThrow(_cursor, "requestDate");
          final int _cursorIndexOfResponseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "responseDate");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final List<CustomerApprovalRequest> _result = new ArrayList<CustomerApprovalRequest>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CustomerApprovalRequest _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpCustomerEmail;
            if (_cursor.isNull(_cursorIndexOfCustomerEmail)) {
              _tmpCustomerEmail = null;
            } else {
              _tmpCustomerEmail = _cursor.getString(_cursorIndexOfCustomerEmail);
            }
            final String _tmpCustomerName;
            if (_cursor.isNull(_cursorIndexOfCustomerName)) {
              _tmpCustomerName = null;
            } else {
              _tmpCustomerName = _cursor.getString(_cursorIndexOfCustomerName);
            }
            final String _tmpCustomerPhone;
            if (_cursor.isNull(_cursorIndexOfCustomerPhone)) {
              _tmpCustomerPhone = null;
            } else {
              _tmpCustomerPhone = _cursor.getString(_cursorIndexOfCustomerPhone);
            }
            final String _tmpReferrerCode;
            if (_cursor.isNull(_cursorIndexOfReferrerCode)) {
              _tmpReferrerCode = null;
            } else {
              _tmpReferrerCode = _cursor.getString(_cursorIndexOfReferrerCode);
            }
            final String _tmpReferrerId;
            if (_cursor.isNull(_cursorIndexOfReferrerId)) {
              _tmpReferrerId = null;
            } else {
              _tmpReferrerId = _cursor.getString(_cursorIndexOfReferrerId);
            }
            final ApprovalStatus _tmpStatus;
            _tmpStatus = __ApprovalStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final long _tmpRequestDate;
            _tmpRequestDate = _cursor.getLong(_cursorIndexOfRequestDate);
            final Long _tmpResponseDate;
            if (_cursor.isNull(_cursorIndexOfResponseDate)) {
              _tmpResponseDate = null;
            } else {
              _tmpResponseDate = _cursor.getLong(_cursorIndexOfResponseDate);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            _item = new CustomerApprovalRequest(_tmpId,_tmpCustomerEmail,_tmpCustomerName,_tmpCustomerPhone,_tmpReferrerCode,_tmpReferrerId,_tmpStatus,_tmpRequestDate,_tmpResponseDate,_tmpNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CustomerApprovalRequest>> getRequestsByStatus(final ApprovalStatus status) {
    final String _sql = "SELECT * FROM customer_approval_requests WHERE status = ? ORDER BY requestDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, __ApprovalStatus_enumToString(status));
    return CoroutinesRoom.createFlow(__db, false, new String[] {"customer_approval_requests"}, new Callable<List<CustomerApprovalRequest>>() {
      @Override
      @NonNull
      public List<CustomerApprovalRequest> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCustomerEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "customerEmail");
          final int _cursorIndexOfCustomerName = CursorUtil.getColumnIndexOrThrow(_cursor, "customerName");
          final int _cursorIndexOfCustomerPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "customerPhone");
          final int _cursorIndexOfReferrerCode = CursorUtil.getColumnIndexOrThrow(_cursor, "referrerCode");
          final int _cursorIndexOfReferrerId = CursorUtil.getColumnIndexOrThrow(_cursor, "referrerId");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfRequestDate = CursorUtil.getColumnIndexOrThrow(_cursor, "requestDate");
          final int _cursorIndexOfResponseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "responseDate");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final List<CustomerApprovalRequest> _result = new ArrayList<CustomerApprovalRequest>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CustomerApprovalRequest _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpCustomerEmail;
            if (_cursor.isNull(_cursorIndexOfCustomerEmail)) {
              _tmpCustomerEmail = null;
            } else {
              _tmpCustomerEmail = _cursor.getString(_cursorIndexOfCustomerEmail);
            }
            final String _tmpCustomerName;
            if (_cursor.isNull(_cursorIndexOfCustomerName)) {
              _tmpCustomerName = null;
            } else {
              _tmpCustomerName = _cursor.getString(_cursorIndexOfCustomerName);
            }
            final String _tmpCustomerPhone;
            if (_cursor.isNull(_cursorIndexOfCustomerPhone)) {
              _tmpCustomerPhone = null;
            } else {
              _tmpCustomerPhone = _cursor.getString(_cursorIndexOfCustomerPhone);
            }
            final String _tmpReferrerCode;
            if (_cursor.isNull(_cursorIndexOfReferrerCode)) {
              _tmpReferrerCode = null;
            } else {
              _tmpReferrerCode = _cursor.getString(_cursorIndexOfReferrerCode);
            }
            final String _tmpReferrerId;
            if (_cursor.isNull(_cursorIndexOfReferrerId)) {
              _tmpReferrerId = null;
            } else {
              _tmpReferrerId = _cursor.getString(_cursorIndexOfReferrerId);
            }
            final ApprovalStatus _tmpStatus;
            _tmpStatus = __ApprovalStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final long _tmpRequestDate;
            _tmpRequestDate = _cursor.getLong(_cursorIndexOfRequestDate);
            final Long _tmpResponseDate;
            if (_cursor.isNull(_cursorIndexOfResponseDate)) {
              _tmpResponseDate = null;
            } else {
              _tmpResponseDate = _cursor.getLong(_cursorIndexOfResponseDate);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            _item = new CustomerApprovalRequest(_tmpId,_tmpCustomerEmail,_tmpCustomerName,_tmpCustomerPhone,_tmpReferrerCode,_tmpReferrerId,_tmpStatus,_tmpRequestDate,_tmpResponseDate,_tmpNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getRequestByCustomerEmail(final String email,
      final Continuation<? super CustomerApprovalRequest> $completion) {
    final String _sql = "SELECT * FROM customer_approval_requests WHERE customerEmail = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (email == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, email);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CustomerApprovalRequest>() {
      @Override
      @Nullable
      public CustomerApprovalRequest call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCustomerEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "customerEmail");
          final int _cursorIndexOfCustomerName = CursorUtil.getColumnIndexOrThrow(_cursor, "customerName");
          final int _cursorIndexOfCustomerPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "customerPhone");
          final int _cursorIndexOfReferrerCode = CursorUtil.getColumnIndexOrThrow(_cursor, "referrerCode");
          final int _cursorIndexOfReferrerId = CursorUtil.getColumnIndexOrThrow(_cursor, "referrerId");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfRequestDate = CursorUtil.getColumnIndexOrThrow(_cursor, "requestDate");
          final int _cursorIndexOfResponseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "responseDate");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final CustomerApprovalRequest _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpCustomerEmail;
            if (_cursor.isNull(_cursorIndexOfCustomerEmail)) {
              _tmpCustomerEmail = null;
            } else {
              _tmpCustomerEmail = _cursor.getString(_cursorIndexOfCustomerEmail);
            }
            final String _tmpCustomerName;
            if (_cursor.isNull(_cursorIndexOfCustomerName)) {
              _tmpCustomerName = null;
            } else {
              _tmpCustomerName = _cursor.getString(_cursorIndexOfCustomerName);
            }
            final String _tmpCustomerPhone;
            if (_cursor.isNull(_cursorIndexOfCustomerPhone)) {
              _tmpCustomerPhone = null;
            } else {
              _tmpCustomerPhone = _cursor.getString(_cursorIndexOfCustomerPhone);
            }
            final String _tmpReferrerCode;
            if (_cursor.isNull(_cursorIndexOfReferrerCode)) {
              _tmpReferrerCode = null;
            } else {
              _tmpReferrerCode = _cursor.getString(_cursorIndexOfReferrerCode);
            }
            final String _tmpReferrerId;
            if (_cursor.isNull(_cursorIndexOfReferrerId)) {
              _tmpReferrerId = null;
            } else {
              _tmpReferrerId = _cursor.getString(_cursorIndexOfReferrerId);
            }
            final ApprovalStatus _tmpStatus;
            _tmpStatus = __ApprovalStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final long _tmpRequestDate;
            _tmpRequestDate = _cursor.getLong(_cursorIndexOfRequestDate);
            final Long _tmpResponseDate;
            if (_cursor.isNull(_cursorIndexOfResponseDate)) {
              _tmpResponseDate = null;
            } else {
              _tmpResponseDate = _cursor.getLong(_cursorIndexOfResponseDate);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            _result = new CustomerApprovalRequest(_tmpId,_tmpCustomerEmail,_tmpCustomerName,_tmpCustomerPhone,_tmpReferrerCode,_tmpReferrerId,_tmpStatus,_tmpRequestDate,_tmpResponseDate,_tmpNotes);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPendingRequestsByReferrerCode(final String code,
      final Continuation<? super List<CustomerApprovalRequest>> $completion) {
    final String _sql = "SELECT * FROM customer_approval_requests WHERE referrerCode = ? AND status = 'pending'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (code == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, code);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CustomerApprovalRequest>>() {
      @Override
      @NonNull
      public List<CustomerApprovalRequest> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCustomerEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "customerEmail");
          final int _cursorIndexOfCustomerName = CursorUtil.getColumnIndexOrThrow(_cursor, "customerName");
          final int _cursorIndexOfCustomerPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "customerPhone");
          final int _cursorIndexOfReferrerCode = CursorUtil.getColumnIndexOrThrow(_cursor, "referrerCode");
          final int _cursorIndexOfReferrerId = CursorUtil.getColumnIndexOrThrow(_cursor, "referrerId");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfRequestDate = CursorUtil.getColumnIndexOrThrow(_cursor, "requestDate");
          final int _cursorIndexOfResponseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "responseDate");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final List<CustomerApprovalRequest> _result = new ArrayList<CustomerApprovalRequest>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CustomerApprovalRequest _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpCustomerEmail;
            if (_cursor.isNull(_cursorIndexOfCustomerEmail)) {
              _tmpCustomerEmail = null;
            } else {
              _tmpCustomerEmail = _cursor.getString(_cursorIndexOfCustomerEmail);
            }
            final String _tmpCustomerName;
            if (_cursor.isNull(_cursorIndexOfCustomerName)) {
              _tmpCustomerName = null;
            } else {
              _tmpCustomerName = _cursor.getString(_cursorIndexOfCustomerName);
            }
            final String _tmpCustomerPhone;
            if (_cursor.isNull(_cursorIndexOfCustomerPhone)) {
              _tmpCustomerPhone = null;
            } else {
              _tmpCustomerPhone = _cursor.getString(_cursorIndexOfCustomerPhone);
            }
            final String _tmpReferrerCode;
            if (_cursor.isNull(_cursorIndexOfReferrerCode)) {
              _tmpReferrerCode = null;
            } else {
              _tmpReferrerCode = _cursor.getString(_cursorIndexOfReferrerCode);
            }
            final String _tmpReferrerId;
            if (_cursor.isNull(_cursorIndexOfReferrerId)) {
              _tmpReferrerId = null;
            } else {
              _tmpReferrerId = _cursor.getString(_cursorIndexOfReferrerId);
            }
            final ApprovalStatus _tmpStatus;
            _tmpStatus = __ApprovalStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final long _tmpRequestDate;
            _tmpRequestDate = _cursor.getLong(_cursorIndexOfRequestDate);
            final Long _tmpResponseDate;
            if (_cursor.isNull(_cursorIndexOfResponseDate)) {
              _tmpResponseDate = null;
            } else {
              _tmpResponseDate = _cursor.getLong(_cursorIndexOfResponseDate);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            _item = new CustomerApprovalRequest(_tmpId,_tmpCustomerEmail,_tmpCustomerName,_tmpCustomerPhone,_tmpReferrerCode,_tmpReferrerId,_tmpStatus,_tmpRequestDate,_tmpResponseDate,_tmpNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPendingRequestsCount(final String referrerId,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM customer_approval_requests WHERE referrerId = ? AND status = 'pending'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (referrerId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, referrerId);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }

  private String __ApprovalStatus_enumToString(@NonNull final ApprovalStatus _value) {
    switch (_value) {
      case PENDING: return "PENDING";
      case APPROVED: return "APPROVED";
      case REJECTED: return "REJECTED";
      case EXPIRED: return "EXPIRED";
      default: throw new IllegalArgumentException("Can't convert enum to string, unknown enum value: " + _value);
    }
  }

  private ApprovalStatus __ApprovalStatus_stringToEnum(@NonNull final String _value) {
    switch (_value) {
      case "PENDING": return ApprovalStatus.PENDING;
      case "APPROVED": return ApprovalStatus.APPROVED;
      case "REJECTED": return ApprovalStatus.REJECTED;
      case "EXPIRED": return ApprovalStatus.EXPIRED;
      default: throw new IllegalArgumentException("Can't convert value to enum, unknown value: " + _value);
    }
  }
}

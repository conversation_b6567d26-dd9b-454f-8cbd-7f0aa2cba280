package com.example.sharenshop.data.local.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.sharenshop.data.local.converter.BigDecimalConverter;
import com.example.sharenshop.data.model.Invoice;
import com.example.sharenshop.data.model.InvoicePaymentType;
import com.example.sharenshop.data.model.InvoiceStatus;
import com.example.sharenshop.data.model.SaleType;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.IllegalArgumentException;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class InvoiceDao_Impl implements InvoiceDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Invoice> __insertionAdapterOfInvoice;

  private final BigDecimalConverter __bigDecimalConverter = new BigDecimalConverter();

  private final EntityDeletionOrUpdateAdapter<Invoice> __updateAdapterOfInvoice;

  private final SharedSQLiteStatement __preparedStmtOfDeleteInvoiceById;

  public InvoiceDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfInvoice = new EntityInsertionAdapter<Invoice>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `invoices` (`id`,`invoiceNumber`,`customerId`,`sellerId`,`adminId`,`subtotalAmount`,`discountAmount`,`taxAmount`,`totalAmount`,`paidAmount`,`remainingAmount`,`status`,`paymentType`,`saleType`,`createdAt`,`updatedAt`,`dueDate`,`paidAt`,`notes`,`customerNotes`,`internalNotes`,`isApproved`,`approvedAt`,`approvedBy`,`latitude`,`longitude`,`address`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Invoice entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getInvoiceNumber() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getInvoiceNumber());
        }
        if (entity.getCustomerId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getCustomerId());
        }
        if (entity.getSellerId() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getSellerId());
        }
        if (entity.getAdminId() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getAdminId());
        }
        final String _tmp = __bigDecimalConverter.fromBigDecimal(entity.getSubtotalAmount());
        if (_tmp == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, _tmp);
        }
        final String _tmp_1 = __bigDecimalConverter.fromBigDecimal(entity.getDiscountAmount());
        if (_tmp_1 == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp_1);
        }
        final String _tmp_2 = __bigDecimalConverter.fromBigDecimal(entity.getTaxAmount());
        if (_tmp_2 == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp_2);
        }
        final String _tmp_3 = __bigDecimalConverter.fromBigDecimal(entity.getTotalAmount());
        if (_tmp_3 == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp_3);
        }
        final String _tmp_4 = __bigDecimalConverter.fromBigDecimal(entity.getPaidAmount());
        if (_tmp_4 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_4);
        }
        final String _tmp_5 = __bigDecimalConverter.fromBigDecimal(entity.getRemainingAmount());
        if (_tmp_5 == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, _tmp_5);
        }
        statement.bindString(12, __InvoiceStatus_enumToString(entity.getStatus()));
        statement.bindString(13, __InvoicePaymentType_enumToString(entity.getPaymentType()));
        statement.bindString(14, __SaleType_enumToString(entity.getSaleType()));
        statement.bindLong(15, entity.getCreatedAt());
        if (entity.getUpdatedAt() == null) {
          statement.bindNull(16);
        } else {
          statement.bindLong(16, entity.getUpdatedAt());
        }
        if (entity.getDueDate() == null) {
          statement.bindNull(17);
        } else {
          statement.bindLong(17, entity.getDueDate());
        }
        if (entity.getPaidAt() == null) {
          statement.bindNull(18);
        } else {
          statement.bindLong(18, entity.getPaidAt());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getNotes());
        }
        if (entity.getCustomerNotes() == null) {
          statement.bindNull(20);
        } else {
          statement.bindString(20, entity.getCustomerNotes());
        }
        if (entity.getInternalNotes() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getInternalNotes());
        }
        final int _tmp_6 = entity.isApproved() ? 1 : 0;
        statement.bindLong(22, _tmp_6);
        if (entity.getApprovedAt() == null) {
          statement.bindNull(23);
        } else {
          statement.bindLong(23, entity.getApprovedAt());
        }
        if (entity.getApprovedBy() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getApprovedBy());
        }
        if (entity.getLatitude() == null) {
          statement.bindNull(25);
        } else {
          statement.bindDouble(25, entity.getLatitude());
        }
        if (entity.getLongitude() == null) {
          statement.bindNull(26);
        } else {
          statement.bindDouble(26, entity.getLongitude());
        }
        if (entity.getAddress() == null) {
          statement.bindNull(27);
        } else {
          statement.bindString(27, entity.getAddress());
        }
      }
    };
    this.__updateAdapterOfInvoice = new EntityDeletionOrUpdateAdapter<Invoice>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `invoices` SET `id` = ?,`invoiceNumber` = ?,`customerId` = ?,`sellerId` = ?,`adminId` = ?,`subtotalAmount` = ?,`discountAmount` = ?,`taxAmount` = ?,`totalAmount` = ?,`paidAmount` = ?,`remainingAmount` = ?,`status` = ?,`paymentType` = ?,`saleType` = ?,`createdAt` = ?,`updatedAt` = ?,`dueDate` = ?,`paidAt` = ?,`notes` = ?,`customerNotes` = ?,`internalNotes` = ?,`isApproved` = ?,`approvedAt` = ?,`approvedBy` = ?,`latitude` = ?,`longitude` = ?,`address` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Invoice entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getInvoiceNumber() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getInvoiceNumber());
        }
        if (entity.getCustomerId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getCustomerId());
        }
        if (entity.getSellerId() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getSellerId());
        }
        if (entity.getAdminId() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getAdminId());
        }
        final String _tmp = __bigDecimalConverter.fromBigDecimal(entity.getSubtotalAmount());
        if (_tmp == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, _tmp);
        }
        final String _tmp_1 = __bigDecimalConverter.fromBigDecimal(entity.getDiscountAmount());
        if (_tmp_1 == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp_1);
        }
        final String _tmp_2 = __bigDecimalConverter.fromBigDecimal(entity.getTaxAmount());
        if (_tmp_2 == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp_2);
        }
        final String _tmp_3 = __bigDecimalConverter.fromBigDecimal(entity.getTotalAmount());
        if (_tmp_3 == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp_3);
        }
        final String _tmp_4 = __bigDecimalConverter.fromBigDecimal(entity.getPaidAmount());
        if (_tmp_4 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_4);
        }
        final String _tmp_5 = __bigDecimalConverter.fromBigDecimal(entity.getRemainingAmount());
        if (_tmp_5 == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, _tmp_5);
        }
        statement.bindString(12, __InvoiceStatus_enumToString(entity.getStatus()));
        statement.bindString(13, __InvoicePaymentType_enumToString(entity.getPaymentType()));
        statement.bindString(14, __SaleType_enumToString(entity.getSaleType()));
        statement.bindLong(15, entity.getCreatedAt());
        if (entity.getUpdatedAt() == null) {
          statement.bindNull(16);
        } else {
          statement.bindLong(16, entity.getUpdatedAt());
        }
        if (entity.getDueDate() == null) {
          statement.bindNull(17);
        } else {
          statement.bindLong(17, entity.getDueDate());
        }
        if (entity.getPaidAt() == null) {
          statement.bindNull(18);
        } else {
          statement.bindLong(18, entity.getPaidAt());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getNotes());
        }
        if (entity.getCustomerNotes() == null) {
          statement.bindNull(20);
        } else {
          statement.bindString(20, entity.getCustomerNotes());
        }
        if (entity.getInternalNotes() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getInternalNotes());
        }
        final int _tmp_6 = entity.isApproved() ? 1 : 0;
        statement.bindLong(22, _tmp_6);
        if (entity.getApprovedAt() == null) {
          statement.bindNull(23);
        } else {
          statement.bindLong(23, entity.getApprovedAt());
        }
        if (entity.getApprovedBy() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getApprovedBy());
        }
        if (entity.getLatitude() == null) {
          statement.bindNull(25);
        } else {
          statement.bindDouble(25, entity.getLatitude());
        }
        if (entity.getLongitude() == null) {
          statement.bindNull(26);
        } else {
          statement.bindDouble(26, entity.getLongitude());
        }
        if (entity.getAddress() == null) {
          statement.bindNull(27);
        } else {
          statement.bindString(27, entity.getAddress());
        }
        if (entity.getId() == null) {
          statement.bindNull(28);
        } else {
          statement.bindString(28, entity.getId());
        }
      }
    };
    this.__preparedStmtOfDeleteInvoiceById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM invoices WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertInvoice(final Invoice invoice, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfInvoice.insert(invoice);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateInvoice(final Invoice invoice, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfInvoice.handle(invoice);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteInvoiceById(final String invoiceId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteInvoiceById.acquire();
        int _argIndex = 1;
        if (invoiceId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, invoiceId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteInvoiceById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<Invoice> getInvoiceById(final String invoiceId) {
    final String _sql = "SELECT * FROM invoices WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (invoiceId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, invoiceId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"invoices"}, new Callable<Invoice>() {
      @Override
      @Nullable
      public Invoice call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfInvoiceNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "invoiceNumber");
          final int _cursorIndexOfCustomerId = CursorUtil.getColumnIndexOrThrow(_cursor, "customerId");
          final int _cursorIndexOfSellerId = CursorUtil.getColumnIndexOrThrow(_cursor, "sellerId");
          final int _cursorIndexOfAdminId = CursorUtil.getColumnIndexOrThrow(_cursor, "adminId");
          final int _cursorIndexOfSubtotalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "subtotalAmount");
          final int _cursorIndexOfDiscountAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "discountAmount");
          final int _cursorIndexOfTaxAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "taxAmount");
          final int _cursorIndexOfTotalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "totalAmount");
          final int _cursorIndexOfPaidAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "paidAmount");
          final int _cursorIndexOfRemainingAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingAmount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentType = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentType");
          final int _cursorIndexOfSaleType = CursorUtil.getColumnIndexOrThrow(_cursor, "saleType");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfPaidAt = CursorUtil.getColumnIndexOrThrow(_cursor, "paidAt");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCustomerNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "customerNotes");
          final int _cursorIndexOfInternalNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "internalNotes");
          final int _cursorIndexOfIsApproved = CursorUtil.getColumnIndexOrThrow(_cursor, "isApproved");
          final int _cursorIndexOfApprovedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedAt");
          final int _cursorIndexOfApprovedBy = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedBy");
          final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
          final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final Invoice _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpInvoiceNumber;
            if (_cursor.isNull(_cursorIndexOfInvoiceNumber)) {
              _tmpInvoiceNumber = null;
            } else {
              _tmpInvoiceNumber = _cursor.getString(_cursorIndexOfInvoiceNumber);
            }
            final String _tmpCustomerId;
            if (_cursor.isNull(_cursorIndexOfCustomerId)) {
              _tmpCustomerId = null;
            } else {
              _tmpCustomerId = _cursor.getString(_cursorIndexOfCustomerId);
            }
            final String _tmpSellerId;
            if (_cursor.isNull(_cursorIndexOfSellerId)) {
              _tmpSellerId = null;
            } else {
              _tmpSellerId = _cursor.getString(_cursorIndexOfSellerId);
            }
            final String _tmpAdminId;
            if (_cursor.isNull(_cursorIndexOfAdminId)) {
              _tmpAdminId = null;
            } else {
              _tmpAdminId = _cursor.getString(_cursorIndexOfAdminId);
            }
            final BigDecimal _tmpSubtotalAmount;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfSubtotalAmount)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfSubtotalAmount);
            }
            _tmpSubtotalAmount = __bigDecimalConverter.toBigDecimal(_tmp);
            final BigDecimal _tmpDiscountAmount;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDiscountAmount)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfDiscountAmount);
            }
            _tmpDiscountAmount = __bigDecimalConverter.toBigDecimal(_tmp_1);
            final BigDecimal _tmpTaxAmount;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTaxAmount)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTaxAmount);
            }
            _tmpTaxAmount = __bigDecimalConverter.toBigDecimal(_tmp_2);
            final BigDecimal _tmpTotalAmount;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfTotalAmount)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfTotalAmount);
            }
            _tmpTotalAmount = __bigDecimalConverter.toBigDecimal(_tmp_3);
            final BigDecimal _tmpPaidAmount;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfPaidAmount)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfPaidAmount);
            }
            _tmpPaidAmount = __bigDecimalConverter.toBigDecimal(_tmp_4);
            final BigDecimal _tmpRemainingAmount;
            final String _tmp_5;
            if (_cursor.isNull(_cursorIndexOfRemainingAmount)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getString(_cursorIndexOfRemainingAmount);
            }
            _tmpRemainingAmount = __bigDecimalConverter.toBigDecimal(_tmp_5);
            final InvoiceStatus _tmpStatus;
            _tmpStatus = __InvoiceStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final InvoicePaymentType _tmpPaymentType;
            _tmpPaymentType = __InvoicePaymentType_stringToEnum(_cursor.getString(_cursorIndexOfPaymentType));
            final SaleType _tmpSaleType;
            _tmpSaleType = __SaleType_stringToEnum(_cursor.getString(_cursorIndexOfSaleType));
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpUpdatedAt;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmpUpdatedAt = null;
            } else {
              _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Long _tmpDueDate;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmpDueDate = null;
            } else {
              _tmpDueDate = _cursor.getLong(_cursorIndexOfDueDate);
            }
            final Long _tmpPaidAt;
            if (_cursor.isNull(_cursorIndexOfPaidAt)) {
              _tmpPaidAt = null;
            } else {
              _tmpPaidAt = _cursor.getLong(_cursorIndexOfPaidAt);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpCustomerNotes;
            if (_cursor.isNull(_cursorIndexOfCustomerNotes)) {
              _tmpCustomerNotes = null;
            } else {
              _tmpCustomerNotes = _cursor.getString(_cursorIndexOfCustomerNotes);
            }
            final String _tmpInternalNotes;
            if (_cursor.isNull(_cursorIndexOfInternalNotes)) {
              _tmpInternalNotes = null;
            } else {
              _tmpInternalNotes = _cursor.getString(_cursorIndexOfInternalNotes);
            }
            final boolean _tmpIsApproved;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsApproved);
            _tmpIsApproved = _tmp_6 != 0;
            final Long _tmpApprovedAt;
            if (_cursor.isNull(_cursorIndexOfApprovedAt)) {
              _tmpApprovedAt = null;
            } else {
              _tmpApprovedAt = _cursor.getLong(_cursorIndexOfApprovedAt);
            }
            final String _tmpApprovedBy;
            if (_cursor.isNull(_cursorIndexOfApprovedBy)) {
              _tmpApprovedBy = null;
            } else {
              _tmpApprovedBy = _cursor.getString(_cursorIndexOfApprovedBy);
            }
            final Double _tmpLatitude;
            if (_cursor.isNull(_cursorIndexOfLatitude)) {
              _tmpLatitude = null;
            } else {
              _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
            }
            final Double _tmpLongitude;
            if (_cursor.isNull(_cursorIndexOfLongitude)) {
              _tmpLongitude = null;
            } else {
              _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            _result = new Invoice(_tmpId,_tmpInvoiceNumber,_tmpCustomerId,_tmpSellerId,_tmpAdminId,_tmpSubtotalAmount,_tmpDiscountAmount,_tmpTaxAmount,_tmpTotalAmount,_tmpPaidAmount,_tmpRemainingAmount,_tmpStatus,_tmpPaymentType,_tmpSaleType,_tmpCreatedAt,_tmpUpdatedAt,_tmpDueDate,_tmpPaidAt,_tmpNotes,_tmpCustomerNotes,_tmpInternalNotes,_tmpIsApproved,_tmpApprovedAt,_tmpApprovedBy,_tmpLatitude,_tmpLongitude,_tmpAddress);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Invoice>> getAllInvoices() {
    final String _sql = "SELECT * FROM invoices";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"invoices"}, new Callable<List<Invoice>>() {
      @Override
      @NonNull
      public List<Invoice> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfInvoiceNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "invoiceNumber");
          final int _cursorIndexOfCustomerId = CursorUtil.getColumnIndexOrThrow(_cursor, "customerId");
          final int _cursorIndexOfSellerId = CursorUtil.getColumnIndexOrThrow(_cursor, "sellerId");
          final int _cursorIndexOfAdminId = CursorUtil.getColumnIndexOrThrow(_cursor, "adminId");
          final int _cursorIndexOfSubtotalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "subtotalAmount");
          final int _cursorIndexOfDiscountAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "discountAmount");
          final int _cursorIndexOfTaxAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "taxAmount");
          final int _cursorIndexOfTotalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "totalAmount");
          final int _cursorIndexOfPaidAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "paidAmount");
          final int _cursorIndexOfRemainingAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingAmount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentType = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentType");
          final int _cursorIndexOfSaleType = CursorUtil.getColumnIndexOrThrow(_cursor, "saleType");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfPaidAt = CursorUtil.getColumnIndexOrThrow(_cursor, "paidAt");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCustomerNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "customerNotes");
          final int _cursorIndexOfInternalNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "internalNotes");
          final int _cursorIndexOfIsApproved = CursorUtil.getColumnIndexOrThrow(_cursor, "isApproved");
          final int _cursorIndexOfApprovedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedAt");
          final int _cursorIndexOfApprovedBy = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedBy");
          final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
          final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final List<Invoice> _result = new ArrayList<Invoice>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Invoice _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpInvoiceNumber;
            if (_cursor.isNull(_cursorIndexOfInvoiceNumber)) {
              _tmpInvoiceNumber = null;
            } else {
              _tmpInvoiceNumber = _cursor.getString(_cursorIndexOfInvoiceNumber);
            }
            final String _tmpCustomerId;
            if (_cursor.isNull(_cursorIndexOfCustomerId)) {
              _tmpCustomerId = null;
            } else {
              _tmpCustomerId = _cursor.getString(_cursorIndexOfCustomerId);
            }
            final String _tmpSellerId;
            if (_cursor.isNull(_cursorIndexOfSellerId)) {
              _tmpSellerId = null;
            } else {
              _tmpSellerId = _cursor.getString(_cursorIndexOfSellerId);
            }
            final String _tmpAdminId;
            if (_cursor.isNull(_cursorIndexOfAdminId)) {
              _tmpAdminId = null;
            } else {
              _tmpAdminId = _cursor.getString(_cursorIndexOfAdminId);
            }
            final BigDecimal _tmpSubtotalAmount;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfSubtotalAmount)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfSubtotalAmount);
            }
            _tmpSubtotalAmount = __bigDecimalConverter.toBigDecimal(_tmp);
            final BigDecimal _tmpDiscountAmount;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDiscountAmount)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfDiscountAmount);
            }
            _tmpDiscountAmount = __bigDecimalConverter.toBigDecimal(_tmp_1);
            final BigDecimal _tmpTaxAmount;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTaxAmount)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTaxAmount);
            }
            _tmpTaxAmount = __bigDecimalConverter.toBigDecimal(_tmp_2);
            final BigDecimal _tmpTotalAmount;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfTotalAmount)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfTotalAmount);
            }
            _tmpTotalAmount = __bigDecimalConverter.toBigDecimal(_tmp_3);
            final BigDecimal _tmpPaidAmount;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfPaidAmount)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfPaidAmount);
            }
            _tmpPaidAmount = __bigDecimalConverter.toBigDecimal(_tmp_4);
            final BigDecimal _tmpRemainingAmount;
            final String _tmp_5;
            if (_cursor.isNull(_cursorIndexOfRemainingAmount)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getString(_cursorIndexOfRemainingAmount);
            }
            _tmpRemainingAmount = __bigDecimalConverter.toBigDecimal(_tmp_5);
            final InvoiceStatus _tmpStatus;
            _tmpStatus = __InvoiceStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final InvoicePaymentType _tmpPaymentType;
            _tmpPaymentType = __InvoicePaymentType_stringToEnum(_cursor.getString(_cursorIndexOfPaymentType));
            final SaleType _tmpSaleType;
            _tmpSaleType = __SaleType_stringToEnum(_cursor.getString(_cursorIndexOfSaleType));
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpUpdatedAt;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmpUpdatedAt = null;
            } else {
              _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Long _tmpDueDate;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmpDueDate = null;
            } else {
              _tmpDueDate = _cursor.getLong(_cursorIndexOfDueDate);
            }
            final Long _tmpPaidAt;
            if (_cursor.isNull(_cursorIndexOfPaidAt)) {
              _tmpPaidAt = null;
            } else {
              _tmpPaidAt = _cursor.getLong(_cursorIndexOfPaidAt);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpCustomerNotes;
            if (_cursor.isNull(_cursorIndexOfCustomerNotes)) {
              _tmpCustomerNotes = null;
            } else {
              _tmpCustomerNotes = _cursor.getString(_cursorIndexOfCustomerNotes);
            }
            final String _tmpInternalNotes;
            if (_cursor.isNull(_cursorIndexOfInternalNotes)) {
              _tmpInternalNotes = null;
            } else {
              _tmpInternalNotes = _cursor.getString(_cursorIndexOfInternalNotes);
            }
            final boolean _tmpIsApproved;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsApproved);
            _tmpIsApproved = _tmp_6 != 0;
            final Long _tmpApprovedAt;
            if (_cursor.isNull(_cursorIndexOfApprovedAt)) {
              _tmpApprovedAt = null;
            } else {
              _tmpApprovedAt = _cursor.getLong(_cursorIndexOfApprovedAt);
            }
            final String _tmpApprovedBy;
            if (_cursor.isNull(_cursorIndexOfApprovedBy)) {
              _tmpApprovedBy = null;
            } else {
              _tmpApprovedBy = _cursor.getString(_cursorIndexOfApprovedBy);
            }
            final Double _tmpLatitude;
            if (_cursor.isNull(_cursorIndexOfLatitude)) {
              _tmpLatitude = null;
            } else {
              _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
            }
            final Double _tmpLongitude;
            if (_cursor.isNull(_cursorIndexOfLongitude)) {
              _tmpLongitude = null;
            } else {
              _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            _item = new Invoice(_tmpId,_tmpInvoiceNumber,_tmpCustomerId,_tmpSellerId,_tmpAdminId,_tmpSubtotalAmount,_tmpDiscountAmount,_tmpTaxAmount,_tmpTotalAmount,_tmpPaidAmount,_tmpRemainingAmount,_tmpStatus,_tmpPaymentType,_tmpSaleType,_tmpCreatedAt,_tmpUpdatedAt,_tmpDueDate,_tmpPaidAt,_tmpNotes,_tmpCustomerNotes,_tmpInternalNotes,_tmpIsApproved,_tmpApprovedAt,_tmpApprovedBy,_tmpLatitude,_tmpLongitude,_tmpAddress);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Invoice>> getInvoicesByCustomerId(final String customerId) {
    final String _sql = "SELECT * FROM invoices WHERE customerId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (customerId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, customerId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"invoices"}, new Callable<List<Invoice>>() {
      @Override
      @NonNull
      public List<Invoice> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfInvoiceNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "invoiceNumber");
          final int _cursorIndexOfCustomerId = CursorUtil.getColumnIndexOrThrow(_cursor, "customerId");
          final int _cursorIndexOfSellerId = CursorUtil.getColumnIndexOrThrow(_cursor, "sellerId");
          final int _cursorIndexOfAdminId = CursorUtil.getColumnIndexOrThrow(_cursor, "adminId");
          final int _cursorIndexOfSubtotalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "subtotalAmount");
          final int _cursorIndexOfDiscountAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "discountAmount");
          final int _cursorIndexOfTaxAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "taxAmount");
          final int _cursorIndexOfTotalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "totalAmount");
          final int _cursorIndexOfPaidAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "paidAmount");
          final int _cursorIndexOfRemainingAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingAmount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentType = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentType");
          final int _cursorIndexOfSaleType = CursorUtil.getColumnIndexOrThrow(_cursor, "saleType");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfPaidAt = CursorUtil.getColumnIndexOrThrow(_cursor, "paidAt");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCustomerNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "customerNotes");
          final int _cursorIndexOfInternalNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "internalNotes");
          final int _cursorIndexOfIsApproved = CursorUtil.getColumnIndexOrThrow(_cursor, "isApproved");
          final int _cursorIndexOfApprovedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedAt");
          final int _cursorIndexOfApprovedBy = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedBy");
          final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
          final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final List<Invoice> _result = new ArrayList<Invoice>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Invoice _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpInvoiceNumber;
            if (_cursor.isNull(_cursorIndexOfInvoiceNumber)) {
              _tmpInvoiceNumber = null;
            } else {
              _tmpInvoiceNumber = _cursor.getString(_cursorIndexOfInvoiceNumber);
            }
            final String _tmpCustomerId;
            if (_cursor.isNull(_cursorIndexOfCustomerId)) {
              _tmpCustomerId = null;
            } else {
              _tmpCustomerId = _cursor.getString(_cursorIndexOfCustomerId);
            }
            final String _tmpSellerId;
            if (_cursor.isNull(_cursorIndexOfSellerId)) {
              _tmpSellerId = null;
            } else {
              _tmpSellerId = _cursor.getString(_cursorIndexOfSellerId);
            }
            final String _tmpAdminId;
            if (_cursor.isNull(_cursorIndexOfAdminId)) {
              _tmpAdminId = null;
            } else {
              _tmpAdminId = _cursor.getString(_cursorIndexOfAdminId);
            }
            final BigDecimal _tmpSubtotalAmount;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfSubtotalAmount)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfSubtotalAmount);
            }
            _tmpSubtotalAmount = __bigDecimalConverter.toBigDecimal(_tmp);
            final BigDecimal _tmpDiscountAmount;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDiscountAmount)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfDiscountAmount);
            }
            _tmpDiscountAmount = __bigDecimalConverter.toBigDecimal(_tmp_1);
            final BigDecimal _tmpTaxAmount;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTaxAmount)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTaxAmount);
            }
            _tmpTaxAmount = __bigDecimalConverter.toBigDecimal(_tmp_2);
            final BigDecimal _tmpTotalAmount;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfTotalAmount)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfTotalAmount);
            }
            _tmpTotalAmount = __bigDecimalConverter.toBigDecimal(_tmp_3);
            final BigDecimal _tmpPaidAmount;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfPaidAmount)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfPaidAmount);
            }
            _tmpPaidAmount = __bigDecimalConverter.toBigDecimal(_tmp_4);
            final BigDecimal _tmpRemainingAmount;
            final String _tmp_5;
            if (_cursor.isNull(_cursorIndexOfRemainingAmount)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getString(_cursorIndexOfRemainingAmount);
            }
            _tmpRemainingAmount = __bigDecimalConverter.toBigDecimal(_tmp_5);
            final InvoiceStatus _tmpStatus;
            _tmpStatus = __InvoiceStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final InvoicePaymentType _tmpPaymentType;
            _tmpPaymentType = __InvoicePaymentType_stringToEnum(_cursor.getString(_cursorIndexOfPaymentType));
            final SaleType _tmpSaleType;
            _tmpSaleType = __SaleType_stringToEnum(_cursor.getString(_cursorIndexOfSaleType));
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpUpdatedAt;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmpUpdatedAt = null;
            } else {
              _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Long _tmpDueDate;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmpDueDate = null;
            } else {
              _tmpDueDate = _cursor.getLong(_cursorIndexOfDueDate);
            }
            final Long _tmpPaidAt;
            if (_cursor.isNull(_cursorIndexOfPaidAt)) {
              _tmpPaidAt = null;
            } else {
              _tmpPaidAt = _cursor.getLong(_cursorIndexOfPaidAt);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpCustomerNotes;
            if (_cursor.isNull(_cursorIndexOfCustomerNotes)) {
              _tmpCustomerNotes = null;
            } else {
              _tmpCustomerNotes = _cursor.getString(_cursorIndexOfCustomerNotes);
            }
            final String _tmpInternalNotes;
            if (_cursor.isNull(_cursorIndexOfInternalNotes)) {
              _tmpInternalNotes = null;
            } else {
              _tmpInternalNotes = _cursor.getString(_cursorIndexOfInternalNotes);
            }
            final boolean _tmpIsApproved;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsApproved);
            _tmpIsApproved = _tmp_6 != 0;
            final Long _tmpApprovedAt;
            if (_cursor.isNull(_cursorIndexOfApprovedAt)) {
              _tmpApprovedAt = null;
            } else {
              _tmpApprovedAt = _cursor.getLong(_cursorIndexOfApprovedAt);
            }
            final String _tmpApprovedBy;
            if (_cursor.isNull(_cursorIndexOfApprovedBy)) {
              _tmpApprovedBy = null;
            } else {
              _tmpApprovedBy = _cursor.getString(_cursorIndexOfApprovedBy);
            }
            final Double _tmpLatitude;
            if (_cursor.isNull(_cursorIndexOfLatitude)) {
              _tmpLatitude = null;
            } else {
              _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
            }
            final Double _tmpLongitude;
            if (_cursor.isNull(_cursorIndexOfLongitude)) {
              _tmpLongitude = null;
            } else {
              _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            _item = new Invoice(_tmpId,_tmpInvoiceNumber,_tmpCustomerId,_tmpSellerId,_tmpAdminId,_tmpSubtotalAmount,_tmpDiscountAmount,_tmpTaxAmount,_tmpTotalAmount,_tmpPaidAmount,_tmpRemainingAmount,_tmpStatus,_tmpPaymentType,_tmpSaleType,_tmpCreatedAt,_tmpUpdatedAt,_tmpDueDate,_tmpPaidAt,_tmpNotes,_tmpCustomerNotes,_tmpInternalNotes,_tmpIsApproved,_tmpApprovedAt,_tmpApprovedBy,_tmpLatitude,_tmpLongitude,_tmpAddress);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Invoice>> getInvoicesBySellerId(final String sellerId) {
    final String _sql = "SELECT * FROM invoices WHERE sellerId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (sellerId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, sellerId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"invoices"}, new Callable<List<Invoice>>() {
      @Override
      @NonNull
      public List<Invoice> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfInvoiceNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "invoiceNumber");
          final int _cursorIndexOfCustomerId = CursorUtil.getColumnIndexOrThrow(_cursor, "customerId");
          final int _cursorIndexOfSellerId = CursorUtil.getColumnIndexOrThrow(_cursor, "sellerId");
          final int _cursorIndexOfAdminId = CursorUtil.getColumnIndexOrThrow(_cursor, "adminId");
          final int _cursorIndexOfSubtotalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "subtotalAmount");
          final int _cursorIndexOfDiscountAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "discountAmount");
          final int _cursorIndexOfTaxAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "taxAmount");
          final int _cursorIndexOfTotalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "totalAmount");
          final int _cursorIndexOfPaidAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "paidAmount");
          final int _cursorIndexOfRemainingAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingAmount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentType = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentType");
          final int _cursorIndexOfSaleType = CursorUtil.getColumnIndexOrThrow(_cursor, "saleType");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfPaidAt = CursorUtil.getColumnIndexOrThrow(_cursor, "paidAt");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCustomerNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "customerNotes");
          final int _cursorIndexOfInternalNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "internalNotes");
          final int _cursorIndexOfIsApproved = CursorUtil.getColumnIndexOrThrow(_cursor, "isApproved");
          final int _cursorIndexOfApprovedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedAt");
          final int _cursorIndexOfApprovedBy = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedBy");
          final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
          final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final List<Invoice> _result = new ArrayList<Invoice>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Invoice _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpInvoiceNumber;
            if (_cursor.isNull(_cursorIndexOfInvoiceNumber)) {
              _tmpInvoiceNumber = null;
            } else {
              _tmpInvoiceNumber = _cursor.getString(_cursorIndexOfInvoiceNumber);
            }
            final String _tmpCustomerId;
            if (_cursor.isNull(_cursorIndexOfCustomerId)) {
              _tmpCustomerId = null;
            } else {
              _tmpCustomerId = _cursor.getString(_cursorIndexOfCustomerId);
            }
            final String _tmpSellerId;
            if (_cursor.isNull(_cursorIndexOfSellerId)) {
              _tmpSellerId = null;
            } else {
              _tmpSellerId = _cursor.getString(_cursorIndexOfSellerId);
            }
            final String _tmpAdminId;
            if (_cursor.isNull(_cursorIndexOfAdminId)) {
              _tmpAdminId = null;
            } else {
              _tmpAdminId = _cursor.getString(_cursorIndexOfAdminId);
            }
            final BigDecimal _tmpSubtotalAmount;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfSubtotalAmount)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfSubtotalAmount);
            }
            _tmpSubtotalAmount = __bigDecimalConverter.toBigDecimal(_tmp);
            final BigDecimal _tmpDiscountAmount;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDiscountAmount)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfDiscountAmount);
            }
            _tmpDiscountAmount = __bigDecimalConverter.toBigDecimal(_tmp_1);
            final BigDecimal _tmpTaxAmount;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTaxAmount)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTaxAmount);
            }
            _tmpTaxAmount = __bigDecimalConverter.toBigDecimal(_tmp_2);
            final BigDecimal _tmpTotalAmount;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfTotalAmount)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfTotalAmount);
            }
            _tmpTotalAmount = __bigDecimalConverter.toBigDecimal(_tmp_3);
            final BigDecimal _tmpPaidAmount;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfPaidAmount)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfPaidAmount);
            }
            _tmpPaidAmount = __bigDecimalConverter.toBigDecimal(_tmp_4);
            final BigDecimal _tmpRemainingAmount;
            final String _tmp_5;
            if (_cursor.isNull(_cursorIndexOfRemainingAmount)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getString(_cursorIndexOfRemainingAmount);
            }
            _tmpRemainingAmount = __bigDecimalConverter.toBigDecimal(_tmp_5);
            final InvoiceStatus _tmpStatus;
            _tmpStatus = __InvoiceStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final InvoicePaymentType _tmpPaymentType;
            _tmpPaymentType = __InvoicePaymentType_stringToEnum(_cursor.getString(_cursorIndexOfPaymentType));
            final SaleType _tmpSaleType;
            _tmpSaleType = __SaleType_stringToEnum(_cursor.getString(_cursorIndexOfSaleType));
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpUpdatedAt;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmpUpdatedAt = null;
            } else {
              _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Long _tmpDueDate;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmpDueDate = null;
            } else {
              _tmpDueDate = _cursor.getLong(_cursorIndexOfDueDate);
            }
            final Long _tmpPaidAt;
            if (_cursor.isNull(_cursorIndexOfPaidAt)) {
              _tmpPaidAt = null;
            } else {
              _tmpPaidAt = _cursor.getLong(_cursorIndexOfPaidAt);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpCustomerNotes;
            if (_cursor.isNull(_cursorIndexOfCustomerNotes)) {
              _tmpCustomerNotes = null;
            } else {
              _tmpCustomerNotes = _cursor.getString(_cursorIndexOfCustomerNotes);
            }
            final String _tmpInternalNotes;
            if (_cursor.isNull(_cursorIndexOfInternalNotes)) {
              _tmpInternalNotes = null;
            } else {
              _tmpInternalNotes = _cursor.getString(_cursorIndexOfInternalNotes);
            }
            final boolean _tmpIsApproved;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsApproved);
            _tmpIsApproved = _tmp_6 != 0;
            final Long _tmpApprovedAt;
            if (_cursor.isNull(_cursorIndexOfApprovedAt)) {
              _tmpApprovedAt = null;
            } else {
              _tmpApprovedAt = _cursor.getLong(_cursorIndexOfApprovedAt);
            }
            final String _tmpApprovedBy;
            if (_cursor.isNull(_cursorIndexOfApprovedBy)) {
              _tmpApprovedBy = null;
            } else {
              _tmpApprovedBy = _cursor.getString(_cursorIndexOfApprovedBy);
            }
            final Double _tmpLatitude;
            if (_cursor.isNull(_cursorIndexOfLatitude)) {
              _tmpLatitude = null;
            } else {
              _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
            }
            final Double _tmpLongitude;
            if (_cursor.isNull(_cursorIndexOfLongitude)) {
              _tmpLongitude = null;
            } else {
              _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            _item = new Invoice(_tmpId,_tmpInvoiceNumber,_tmpCustomerId,_tmpSellerId,_tmpAdminId,_tmpSubtotalAmount,_tmpDiscountAmount,_tmpTaxAmount,_tmpTotalAmount,_tmpPaidAmount,_tmpRemainingAmount,_tmpStatus,_tmpPaymentType,_tmpSaleType,_tmpCreatedAt,_tmpUpdatedAt,_tmpDueDate,_tmpPaidAt,_tmpNotes,_tmpCustomerNotes,_tmpInternalNotes,_tmpIsApproved,_tmpApprovedAt,_tmpApprovedBy,_tmpLatitude,_tmpLongitude,_tmpAddress);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }

  private String __InvoiceStatus_enumToString(@NonNull final InvoiceStatus _value) {
    switch (_value) {
      case DRAFT: return "DRAFT";
      case PENDING: return "PENDING";
      case PARTIALLY_PAID: return "PARTIALLY_PAID";
      case PAID: return "PAID";
      case OVERDUE: return "OVERDUE";
      case CANCELLED: return "CANCELLED";
      case RETURNED: return "RETURNED";
      default: throw new IllegalArgumentException("Can't convert enum to string, unknown enum value: " + _value);
    }
  }

  private String __InvoicePaymentType_enumToString(@NonNull final InvoicePaymentType _value) {
    switch (_value) {
      case CASH: return "CASH";
      case CREDIT: return "CREDIT";
      case MIXED: return "MIXED";
      case INSTALLMENT: return "INSTALLMENT";
      default: throw new IllegalArgumentException("Can't convert enum to string, unknown enum value: " + _value);
    }
  }

  private String __SaleType_enumToString(@NonNull final SaleType _value) {
    switch (_value) {
      case IN_PERSON: return "IN_PERSON";
      case ONLINE: return "ONLINE";
      case PHONE: return "PHONE";
      case WHOLESALE: return "WHOLESALE";
      default: throw new IllegalArgumentException("Can't convert enum to string, unknown enum value: " + _value);
    }
  }

  private InvoiceStatus __InvoiceStatus_stringToEnum(@NonNull final String _value) {
    switch (_value) {
      case "DRAFT": return InvoiceStatus.DRAFT;
      case "PENDING": return InvoiceStatus.PENDING;
      case "PARTIALLY_PAID": return InvoiceStatus.PARTIALLY_PAID;
      case "PAID": return InvoiceStatus.PAID;
      case "OVERDUE": return InvoiceStatus.OVERDUE;
      case "CANCELLED": return InvoiceStatus.CANCELLED;
      case "RETURNED": return InvoiceStatus.RETURNED;
      default: throw new IllegalArgumentException("Can't convert value to enum, unknown value: " + _value);
    }
  }

  private InvoicePaymentType __InvoicePaymentType_stringToEnum(@NonNull final String _value) {
    switch (_value) {
      case "CASH": return InvoicePaymentType.CASH;
      case "CREDIT": return InvoicePaymentType.CREDIT;
      case "MIXED": return InvoicePaymentType.MIXED;
      case "INSTALLMENT": return InvoicePaymentType.INSTALLMENT;
      default: throw new IllegalArgumentException("Can't convert value to enum, unknown value: " + _value);
    }
  }

  private SaleType __SaleType_stringToEnum(@NonNull final String _value) {
    switch (_value) {
      case "IN_PERSON": return SaleType.IN_PERSON;
      case "ONLINE": return SaleType.ONLINE;
      case "PHONE": return SaleType.PHONE;
      case "WHOLESALE": return SaleType.WHOLESALE;
      default: throw new IllegalArgumentException("Can't convert value to enum, unknown value: " + _value);
    }
  }
}

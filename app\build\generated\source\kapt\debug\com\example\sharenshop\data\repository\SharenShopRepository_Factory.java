// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SharenShopRepository_Factory implements Factory<SharenShopRepository> {
  @Override
  public SharenShopRepository get() {
    return newInstance();
  }

  public static SharenShopRepository_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SharenShopRepository newInstance() {
    return new SharenShopRepository();
  }

  private static final class InstanceHolder {
    private static final SharenShopRepository_Factory INSTANCE = new SharenShopRepository_Factory();
  }
}

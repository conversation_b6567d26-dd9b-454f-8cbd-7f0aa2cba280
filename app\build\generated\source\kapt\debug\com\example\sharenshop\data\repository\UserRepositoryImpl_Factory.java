// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.data.repository;

import com.example.sharenshop.data.local.dao.UserDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import io.github.jan.supabase.SupabaseClient;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserRepositoryImpl_Factory implements Factory<UserRepositoryImpl> {
  private final Provider<UserDao> userDaoProvider;

  private final Provider<SupabaseClient> supabaseClientProvider;

  public UserRepositoryImpl_Factory(Provider<UserDao> userDaoProvider,
      Provider<SupabaseClient> supabaseClientProvider) {
    this.userDaoProvider = userDaoProvider;
    this.supabaseClientProvider = supabaseClientProvider;
  }

  @Override
  public UserRepositoryImpl get() {
    return newInstance(userDaoProvider.get(), supabaseClientProvider.get());
  }

  public static UserRepositoryImpl_Factory create(Provider<UserDao> userDaoProvider,
      Provider<SupabaseClient> supabaseClientProvider) {
    return new UserRepositoryImpl_Factory(userDaoProvider, supabaseClientProvider);
  }

  public static UserRepositoryImpl newInstance(UserDao userDao, SupabaseClient supabaseClient) {
    return new UserRepositoryImpl(userDao, supabaseClient);
  }
}

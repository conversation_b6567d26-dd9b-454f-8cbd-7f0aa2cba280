// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.database.SharenShopDatabase;
import com.example.sharenshop.data.database.dao.InventoryTransactionDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideInventoryTransactionDaoFactory implements Factory<InventoryTransactionDao> {
  private final Provider<SharenShopDatabase> databaseProvider;

  public DatabaseModule_ProvideInventoryTransactionDaoFactory(
      Provider<SharenShopDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public InventoryTransactionDao get() {
    return provideInventoryTransactionDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideInventoryTransactionDaoFactory create(
      Provider<SharenShopDatabase> databaseProvider) {
    return new DatabaseModule_ProvideInventoryTransactionDaoFactory(databaseProvider);
  }

  public static InventoryTransactionDao provideInventoryTransactionDao(
      SharenShopDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideInventoryTransactionDao(database));
  }
}

package com.example.sharenshop.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.sharenshop.data.local.converter.BigDecimalConverter;
import com.example.sharenshop.data.model.CartItem;
import com.example.sharenshop.data.model.CartPaymentType;
import java.lang.Class;
import java.lang.Exception;
import java.lang.IllegalArgumentException;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class CartDao_Impl implements CartDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<CartItem> __insertionAdapterOfCartItem;

  private final BigDecimalConverter __bigDecimalConverter = new BigDecimalConverter();

  private final SharedSQLiteStatement __preparedStmtOfDeleteItem;

  private final SharedSQLiteStatement __preparedStmtOfClearCartForUser;

  public CartDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfCartItem = new EntityInsertionAdapter<CartItem>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `cart_items` (`id`,`customerId`,`productId`,`variantId`,`quantity`,`unitPrice`,`totalPrice`,`paymentType`,`addedAt`,`updatedAt`) VALUES (?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CartItem entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getCustomerId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCustomerId());
        }
        if (entity.getProductId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getProductId());
        }
        if (entity.getVariantId() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getVariantId());
        }
        statement.bindLong(5, entity.getQuantity());
        final String _tmp = __bigDecimalConverter.fromBigDecimal(entity.getUnitPrice());
        if (_tmp == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, _tmp);
        }
        final String _tmp_1 = __bigDecimalConverter.fromBigDecimal(entity.getTotalPrice());
        if (_tmp_1 == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp_1);
        }
        statement.bindString(8, __CartPaymentType_enumToString(entity.getPaymentType()));
        statement.bindLong(9, entity.getAddedAt());
        if (entity.getUpdatedAt() == null) {
          statement.bindNull(10);
        } else {
          statement.bindLong(10, entity.getUpdatedAt());
        }
      }
    };
    this.__preparedStmtOfDeleteItem = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cart_items WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearCartForUser = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cart_items WHERE customerId = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertItem(final CartItem item, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfCartItem.insert(item);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertItems(final List<CartItem> items,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfCartItem.insert(items);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteItem(final String itemId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteItem.acquire();
        int _argIndex = 1;
        if (itemId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, itemId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteItem.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearCartForUser(final String customerId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearCartForUser.acquire();
        int _argIndex = 1;
        if (customerId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, customerId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearCartForUser.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CartItem>> getCartItemsForUser(final String customerId) {
    final String _sql = "SELECT * FROM cart_items WHERE customerId = ? ORDER BY addedAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (customerId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, customerId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cart_items"}, new Callable<List<CartItem>>() {
      @Override
      @NonNull
      public List<CartItem> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCustomerId = CursorUtil.getColumnIndexOrThrow(_cursor, "customerId");
          final int _cursorIndexOfProductId = CursorUtil.getColumnIndexOrThrow(_cursor, "productId");
          final int _cursorIndexOfVariantId = CursorUtil.getColumnIndexOrThrow(_cursor, "variantId");
          final int _cursorIndexOfQuantity = CursorUtil.getColumnIndexOrThrow(_cursor, "quantity");
          final int _cursorIndexOfUnitPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "unitPrice");
          final int _cursorIndexOfTotalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPrice");
          final int _cursorIndexOfPaymentType = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentType");
          final int _cursorIndexOfAddedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "addedAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<CartItem> _result = new ArrayList<CartItem>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CartItem _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpCustomerId;
            if (_cursor.isNull(_cursorIndexOfCustomerId)) {
              _tmpCustomerId = null;
            } else {
              _tmpCustomerId = _cursor.getString(_cursorIndexOfCustomerId);
            }
            final String _tmpProductId;
            if (_cursor.isNull(_cursorIndexOfProductId)) {
              _tmpProductId = null;
            } else {
              _tmpProductId = _cursor.getString(_cursorIndexOfProductId);
            }
            final String _tmpVariantId;
            if (_cursor.isNull(_cursorIndexOfVariantId)) {
              _tmpVariantId = null;
            } else {
              _tmpVariantId = _cursor.getString(_cursorIndexOfVariantId);
            }
            final int _tmpQuantity;
            _tmpQuantity = _cursor.getInt(_cursorIndexOfQuantity);
            final BigDecimal _tmpUnitPrice;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfUnitPrice)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfUnitPrice);
            }
            _tmpUnitPrice = __bigDecimalConverter.toBigDecimal(_tmp);
            final BigDecimal _tmpTotalPrice;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTotalPrice)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfTotalPrice);
            }
            _tmpTotalPrice = __bigDecimalConverter.toBigDecimal(_tmp_1);
            final CartPaymentType _tmpPaymentType;
            _tmpPaymentType = __CartPaymentType_stringToEnum(_cursor.getString(_cursorIndexOfPaymentType));
            final long _tmpAddedAt;
            _tmpAddedAt = _cursor.getLong(_cursorIndexOfAddedAt);
            final Long _tmpUpdatedAt;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmpUpdatedAt = null;
            } else {
              _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _item = new CartItem(_tmpId,_tmpCustomerId,_tmpProductId,_tmpVariantId,_tmpQuantity,_tmpUnitPrice,_tmpTotalPrice,_tmpPaymentType,_tmpAddedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getItemById(final String itemId, final Continuation<? super CartItem> $completion) {
    final String _sql = "SELECT * FROM cart_items WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (itemId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, itemId);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CartItem>() {
      @Override
      @Nullable
      public CartItem call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCustomerId = CursorUtil.getColumnIndexOrThrow(_cursor, "customerId");
          final int _cursorIndexOfProductId = CursorUtil.getColumnIndexOrThrow(_cursor, "productId");
          final int _cursorIndexOfVariantId = CursorUtil.getColumnIndexOrThrow(_cursor, "variantId");
          final int _cursorIndexOfQuantity = CursorUtil.getColumnIndexOrThrow(_cursor, "quantity");
          final int _cursorIndexOfUnitPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "unitPrice");
          final int _cursorIndexOfTotalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPrice");
          final int _cursorIndexOfPaymentType = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentType");
          final int _cursorIndexOfAddedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "addedAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final CartItem _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpCustomerId;
            if (_cursor.isNull(_cursorIndexOfCustomerId)) {
              _tmpCustomerId = null;
            } else {
              _tmpCustomerId = _cursor.getString(_cursorIndexOfCustomerId);
            }
            final String _tmpProductId;
            if (_cursor.isNull(_cursorIndexOfProductId)) {
              _tmpProductId = null;
            } else {
              _tmpProductId = _cursor.getString(_cursorIndexOfProductId);
            }
            final String _tmpVariantId;
            if (_cursor.isNull(_cursorIndexOfVariantId)) {
              _tmpVariantId = null;
            } else {
              _tmpVariantId = _cursor.getString(_cursorIndexOfVariantId);
            }
            final int _tmpQuantity;
            _tmpQuantity = _cursor.getInt(_cursorIndexOfQuantity);
            final BigDecimal _tmpUnitPrice;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfUnitPrice)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfUnitPrice);
            }
            _tmpUnitPrice = __bigDecimalConverter.toBigDecimal(_tmp);
            final BigDecimal _tmpTotalPrice;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTotalPrice)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfTotalPrice);
            }
            _tmpTotalPrice = __bigDecimalConverter.toBigDecimal(_tmp_1);
            final CartPaymentType _tmpPaymentType;
            _tmpPaymentType = __CartPaymentType_stringToEnum(_cursor.getString(_cursorIndexOfPaymentType));
            final long _tmpAddedAt;
            _tmpAddedAt = _cursor.getLong(_cursorIndexOfAddedAt);
            final Long _tmpUpdatedAt;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmpUpdatedAt = null;
            } else {
              _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _result = new CartItem(_tmpId,_tmpCustomerId,_tmpProductId,_tmpVariantId,_tmpQuantity,_tmpUnitPrice,_tmpTotalPrice,_tmpPaymentType,_tmpAddedAt,_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object findItemByVariantAndPaymentType(final String customerId, final String variantId,
      final CartPaymentType paymentType, final Continuation<? super CartItem> $completion) {
    final String _sql = "SELECT * FROM cart_items WHERE customerId = ? AND variantId = ? AND paymentType = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (customerId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, customerId);
    }
    _argIndex = 2;
    if (variantId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, variantId);
    }
    _argIndex = 3;
    _statement.bindString(_argIndex, __CartPaymentType_enumToString(paymentType));
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CartItem>() {
      @Override
      @Nullable
      public CartItem call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCustomerId = CursorUtil.getColumnIndexOrThrow(_cursor, "customerId");
          final int _cursorIndexOfProductId = CursorUtil.getColumnIndexOrThrow(_cursor, "productId");
          final int _cursorIndexOfVariantId = CursorUtil.getColumnIndexOrThrow(_cursor, "variantId");
          final int _cursorIndexOfQuantity = CursorUtil.getColumnIndexOrThrow(_cursor, "quantity");
          final int _cursorIndexOfUnitPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "unitPrice");
          final int _cursorIndexOfTotalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPrice");
          final int _cursorIndexOfPaymentType = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentType");
          final int _cursorIndexOfAddedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "addedAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final CartItem _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpCustomerId;
            if (_cursor.isNull(_cursorIndexOfCustomerId)) {
              _tmpCustomerId = null;
            } else {
              _tmpCustomerId = _cursor.getString(_cursorIndexOfCustomerId);
            }
            final String _tmpProductId;
            if (_cursor.isNull(_cursorIndexOfProductId)) {
              _tmpProductId = null;
            } else {
              _tmpProductId = _cursor.getString(_cursorIndexOfProductId);
            }
            final String _tmpVariantId;
            if (_cursor.isNull(_cursorIndexOfVariantId)) {
              _tmpVariantId = null;
            } else {
              _tmpVariantId = _cursor.getString(_cursorIndexOfVariantId);
            }
            final int _tmpQuantity;
            _tmpQuantity = _cursor.getInt(_cursorIndexOfQuantity);
            final BigDecimal _tmpUnitPrice;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfUnitPrice)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfUnitPrice);
            }
            _tmpUnitPrice = __bigDecimalConverter.toBigDecimal(_tmp);
            final BigDecimal _tmpTotalPrice;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTotalPrice)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfTotalPrice);
            }
            _tmpTotalPrice = __bigDecimalConverter.toBigDecimal(_tmp_1);
            final CartPaymentType _tmpPaymentType;
            _tmpPaymentType = __CartPaymentType_stringToEnum(_cursor.getString(_cursorIndexOfPaymentType));
            final long _tmpAddedAt;
            _tmpAddedAt = _cursor.getLong(_cursorIndexOfAddedAt);
            final Long _tmpUpdatedAt;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmpUpdatedAt = null;
            } else {
              _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _result = new CartItem(_tmpId,_tmpCustomerId,_tmpProductId,_tmpVariantId,_tmpQuantity,_tmpUnitPrice,_tmpTotalPrice,_tmpPaymentType,_tmpAddedAt,_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }

  private String __CartPaymentType_enumToString(@NonNull final CartPaymentType _value) {
    switch (_value) {
      case CASH: return "CASH";
      case CREDIT: return "CREDIT";
      default: throw new IllegalArgumentException("Can't convert enum to string, unknown enum value: " + _value);
    }
  }

  private CartPaymentType __CartPaymentType_stringToEnum(@NonNull final String _value) {
    switch (_value) {
      case "CASH": return CartPaymentType.CASH;
      case "CREDIT": return CartPaymentType.CREDIT;
      default: throw new IllegalArgumentException("Can't convert value to enum, unknown value: " + _value);
    }
  }
}

// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CartUseCases_Factory implements Factory<CartUseCases> {
  private final Provider<GetCartUseCase> getCartProvider;

  private final Provider<AddToCartUseCase> addToCartProvider;

  private final Provider<UpdateCartItemQuantityUseCase> updateCartItemQuantityProvider;

  private final Provider<RemoveFromCartUseCase> removeFromCartProvider;

  private final Provider<ClearCartUseCase> clearCartProvider;

  public CartUseCases_Factory(Provider<GetCartUseCase> getCartProvider,
      Provider<AddToCartUseCase> addToCartProvider,
      Provider<UpdateCartItemQuantityUseCase> updateCartItemQuantityProvider,
      Provider<RemoveFromCartUseCase> removeFromCartProvider,
      Provider<ClearCartUseCase> clearCartProvider) {
    this.getCartProvider = getCartProvider;
    this.addToCartProvider = addToCartProvider;
    this.updateCartItemQuantityProvider = updateCartItemQuantityProvider;
    this.removeFromCartProvider = removeFromCartProvider;
    this.clearCartProvider = clearCartProvider;
  }

  @Override
  public CartUseCases get() {
    return newInstance(getCartProvider.get(), addToCartProvider.get(), updateCartItemQuantityProvider.get(), removeFromCartProvider.get(), clearCartProvider.get());
  }

  public static CartUseCases_Factory create(Provider<GetCartUseCase> getCartProvider,
      Provider<AddToCartUseCase> addToCartProvider,
      Provider<UpdateCartItemQuantityUseCase> updateCartItemQuantityProvider,
      Provider<RemoveFromCartUseCase> removeFromCartProvider,
      Provider<ClearCartUseCase> clearCartProvider) {
    return new CartUseCases_Factory(getCartProvider, addToCartProvider, updateCartItemQuantityProvider, removeFromCartProvider, clearCartProvider);
  }

  public static CartUseCases newInstance(GetCartUseCase getCart, AddToCartUseCase addToCart,
      UpdateCartItemQuantityUseCase updateCartItemQuantity, RemoveFromCartUseCase removeFromCart,
      ClearCartUseCase clearCart) {
    return new CartUseCases(getCart, addToCart, updateCartItemQuantity, removeFromCart, clearCart);
  }
}

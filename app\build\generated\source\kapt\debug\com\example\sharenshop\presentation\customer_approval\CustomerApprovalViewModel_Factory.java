// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.presentation.customer_approval;

import com.example.sharenshop.domain.use_case.CustomerApprovalUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CustomerApprovalViewModel_Factory implements Factory<CustomerApprovalViewModel> {
  private final Provider<CustomerApprovalUseCases> customerApprovalUseCasesProvider;

  public CustomerApprovalViewModel_Factory(
      Provider<CustomerApprovalUseCases> customerApprovalUseCasesProvider) {
    this.customerApprovalUseCasesProvider = customerApprovalUseCasesProvider;
  }

  @Override
  public CustomerApprovalViewModel get() {
    return newInstance(customerApprovalUseCasesProvider.get());
  }

  public static CustomerApprovalViewModel_Factory create(
      Provider<CustomerApprovalUseCases> customerApprovalUseCasesProvider) {
    return new CustomerApprovalViewModel_Factory(customerApprovalUseCasesProvider);
  }

  public static CustomerApprovalViewModel newInstance(
      CustomerApprovalUseCases customerApprovalUseCases) {
    return new CustomerApprovalViewModel(customerApprovalUseCases);
  }
}

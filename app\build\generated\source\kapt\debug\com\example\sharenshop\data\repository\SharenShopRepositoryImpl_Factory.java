// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.data.repository;

import com.example.sharenshop.domain.repository.NotificationRepository;
import com.example.sharenshop.domain.repository.StatisticsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SharenShopRepositoryImpl_Factory implements Factory<SharenShopRepositoryImpl> {
  private final Provider<StatisticsRepository> statisticsRepositoryProvider;

  private final Provider<NotificationRepository> notificationRepositoryProvider;

  public SharenShopRepositoryImpl_Factory(
      Provider<StatisticsRepository> statisticsRepositoryProvider,
      Provider<NotificationRepository> notificationRepositoryProvider) {
    this.statisticsRepositoryProvider = statisticsRepositoryProvider;
    this.notificationRepositoryProvider = notificationRepositoryProvider;
  }

  @Override
  public SharenShopRepositoryImpl get() {
    return newInstance(statisticsRepositoryProvider.get(), notificationRepositoryProvider.get());
  }

  public static SharenShopRepositoryImpl_Factory create(
      Provider<StatisticsRepository> statisticsRepositoryProvider,
      Provider<NotificationRepository> notificationRepositoryProvider) {
    return new SharenShopRepositoryImpl_Factory(statisticsRepositoryProvider, notificationRepositoryProvider);
  }

  public static SharenShopRepositoryImpl newInstance(StatisticsRepository statisticsRepository,
      NotificationRepository notificationRepository) {
    return new SharenShopRepositoryImpl(statisticsRepository, notificationRepository);
  }
}

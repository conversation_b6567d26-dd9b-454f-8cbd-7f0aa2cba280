// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.StatisticsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetRecentTransactions_Factory implements Factory<GetRecentTransactions> {
  private final Provider<StatisticsRepository> repositoryProvider;

  public GetRecentTransactions_Factory(Provider<StatisticsRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public GetRecentTransactions get() {
    return newInstance(repositoryProvider.get());
  }

  public static GetRecentTransactions_Factory create(
      Provider<StatisticsRepository> repositoryProvider) {
    return new GetRecentTransactions_Factory(repositoryProvider);
  }

  public static GetRecentTransactions newInstance(StatisticsRepository repository) {
    return new GetRecentTransactions(repository);
  }
}

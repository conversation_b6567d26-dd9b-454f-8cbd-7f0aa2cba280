// Generated by Da<PERSON> (https://dagger.dev).
package com.example.sharenshop.data.repository;

import com.example.sharenshop.data.database.dao.ColorDao;
import com.example.sharenshop.data.database.dao.InventoryTransactionDao;
import com.example.sharenshop.data.database.dao.ProductDao;
import com.example.sharenshop.data.database.dao.ProductVariantDao;
import com.example.sharenshop.data.database.dao.SizeDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProductRepository_Factory implements Factory<ProductRepository> {
  private final Provider<ProductDao> productDaoProvider;

  private final Provider<ProductVariantDao> productVariantDaoProvider;

  private final Provider<ColorDao> colorDaoProvider;

  private final Provider<SizeDao> sizeDaoProvider;

  private final Provider<InventoryTransactionDao> inventoryTransactionDaoProvider;

  public ProductRepository_Factory(Provider<ProductDao> productDaoProvider,
      Provider<ProductVariantDao> productVariantDaoProvider, Provider<ColorDao> colorDaoProvider,
      Provider<SizeDao> sizeDaoProvider,
      Provider<InventoryTransactionDao> inventoryTransactionDaoProvider) {
    this.productDaoProvider = productDaoProvider;
    this.productVariantDaoProvider = productVariantDaoProvider;
    this.colorDaoProvider = colorDaoProvider;
    this.sizeDaoProvider = sizeDaoProvider;
    this.inventoryTransactionDaoProvider = inventoryTransactionDaoProvider;
  }

  @Override
  public ProductRepository get() {
    return newInstance(productDaoProvider.get(), productVariantDaoProvider.get(), colorDaoProvider.get(), sizeDaoProvider.get(), inventoryTransactionDaoProvider.get());
  }

  public static ProductRepository_Factory create(Provider<ProductDao> productDaoProvider,
      Provider<ProductVariantDao> productVariantDaoProvider, Provider<ColorDao> colorDaoProvider,
      Provider<SizeDao> sizeDaoProvider,
      Provider<InventoryTransactionDao> inventoryTransactionDaoProvider) {
    return new ProductRepository_Factory(productDaoProvider, productVariantDaoProvider, colorDaoProvider, sizeDaoProvider, inventoryTransactionDaoProvider);
  }

  public static ProductRepository newInstance(ProductDao productDao,
      ProductVariantDao productVariantDao, ColorDao colorDao, SizeDao sizeDao,
      InventoryTransactionDao inventoryTransactionDao) {
    return new ProductRepository(productDao, productVariantDao, colorDao, sizeDao, inventoryTransactionDao);
  }
}

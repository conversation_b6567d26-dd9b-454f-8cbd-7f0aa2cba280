// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.StatisticsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetProductStatistics_Factory implements Factory<GetProductStatistics> {
  private final Provider<StatisticsRepository> repositoryProvider;

  public GetProductStatistics_Factory(Provider<StatisticsRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public GetProductStatistics get() {
    return newInstance(repositoryProvider.get());
  }

  public static GetProductStatistics_Factory create(
      Provider<StatisticsRepository> repositoryProvider) {
    return new GetProductStatistics_Factory(repositoryProvider);
  }

  public static GetProductStatistics newInstance(StatisticsRepository repository) {
    return new GetProductStatistics(repository);
  }
}

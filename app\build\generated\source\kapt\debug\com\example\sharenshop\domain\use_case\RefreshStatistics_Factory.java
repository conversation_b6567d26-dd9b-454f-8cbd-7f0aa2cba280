// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.StatisticsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RefreshStatistics_Factory implements Factory<RefreshStatistics> {
  private final Provider<StatisticsRepository> repositoryProvider;

  public RefreshStatistics_Factory(Provider<StatisticsRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public RefreshStatistics get() {
    return newInstance(repositoryProvider.get());
  }

  public static RefreshStatistics_Factory create(
      Provider<StatisticsRepository> repositoryProvider) {
    return new RefreshStatistics_Factory(repositoryProvider);
  }

  public static RefreshStatistics newInstance(StatisticsRepository repository) {
    return new RefreshStatistics(repository);
  }
}

// Generated by Da<PERSON> (https://dagger.dev).
package com.example.sharenshop.data.repository;

import com.example.sharenshop.data.local.dao.CustomerApprovalDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CustomerApprovalRepositoryImpl_Factory implements Factory<CustomerApprovalRepositoryImpl> {
  private final Provider<CustomerApprovalDao> customerApprovalDaoProvider;

  public CustomerApprovalRepositoryImpl_Factory(
      Provider<CustomerApprovalDao> customerApprovalDaoProvider) {
    this.customerApprovalDaoProvider = customerApprovalDaoProvider;
  }

  @Override
  public CustomerApprovalRepositoryImpl get() {
    return newInstance(customerApprovalDaoProvider.get());
  }

  public static CustomerApprovalRepositoryImpl_Factory create(
      Provider<CustomerApprovalDao> customerApprovalDaoProvider) {
    return new CustomerApprovalRepositoryImpl_Factory(customerApprovalDaoProvider);
  }

  public static CustomerApprovalRepositoryImpl newInstance(
      CustomerApprovalDao customerApprovalDao) {
    return new CustomerApprovalRepositoryImpl(customerApprovalDao);
  }
}

// Generated by Da<PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.domain.repository.CustomerApprovalRepository;
import com.example.sharenshop.domain.repository.NotificationRepository;
import com.example.sharenshop.domain.repository.UserRepository;
import com.example.sharenshop.domain.use_case.CustomerApprovalUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideCustomerApprovalUseCasesFactory implements Factory<CustomerApprovalUseCases> {
  private final Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  private final Provider<NotificationRepository> notificationRepositoryProvider;

  public UseCaseModule_ProvideCustomerApprovalUseCasesFactory(
      Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider,
      Provider<NotificationRepository> notificationRepositoryProvider) {
    this.customerApprovalRepositoryProvider = customerApprovalRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
    this.notificationRepositoryProvider = notificationRepositoryProvider;
  }

  @Override
  public CustomerApprovalUseCases get() {
    return provideCustomerApprovalUseCases(customerApprovalRepositoryProvider.get(), userRepositoryProvider.get(), notificationRepositoryProvider.get());
  }

  public static UseCaseModule_ProvideCustomerApprovalUseCasesFactory create(
      Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider,
      Provider<NotificationRepository> notificationRepositoryProvider) {
    return new UseCaseModule_ProvideCustomerApprovalUseCasesFactory(customerApprovalRepositoryProvider, userRepositoryProvider, notificationRepositoryProvider);
  }

  public static CustomerApprovalUseCases provideCustomerApprovalUseCases(
      CustomerApprovalRepository customerApprovalRepository, UserRepository userRepository,
      NotificationRepository notificationRepository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideCustomerApprovalUseCases(customerApprovalRepository, userRepository, notificationRepository));
  }
}

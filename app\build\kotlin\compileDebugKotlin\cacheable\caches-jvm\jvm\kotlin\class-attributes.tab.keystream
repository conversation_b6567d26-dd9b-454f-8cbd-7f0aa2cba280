#com.example.sharenshop.MainActivity-com.example.sharenshop.MainActivity.Companion,com.example.sharenshop.SharenShopApplication6com.example.sharenshop.SharenShopApplication.Companion7com.example.sharenshop.data.database.SharenShopDatabaseAcom.example.sharenshop.data.database.SharenShopDatabase.CompanionHcom.example.sharenshop.data.database.SharenShopDatabase.DatabaseCallback8com.example.sharenshop.data.database.DatabaseInitializerBcom.example.sharenshop.data.database.converters.DatabaseConverters3com.example.sharenshop.data.database.dao.ProductDao:<EMAIL>@com.example.sharenshop.data.database.entity.ProductVariantEntityFcom.example.sharenshop.data.database.entity.InventoryTransactionEntity;com.example.sharenshop.data.database.entity.TransactionType;com.example.sharenshop.data.database.entity.PriceCalculator6com.example.sharenshop.data.database.entity.SizeEntity8com.example.sharenshop.data.database.entity.SizeCategory8com.example.sharenshop.data.database.entity.DefaultSizes;com.example.sharenshop.data.datastore.AppSettingsSerializer4com.example.sharenshop.data.language.LanguageManager-com.example.sharenshop.data.language.Language7com.example.sharenshop.data.language.Language.Companion,com.example.sharenshop.data.language.Strings0com.example.sharenshop.data.local.SessionManager:com.example.sharenshop.data.local.SessionManager.Companion?com.example.sharenshop.data.local.converter.BigDecimalConverter;com.example.sharenshop.data.local.converters.TypeConverters-com.example.sharenshop.data.local.dao.CartDao9com.example.sharenshop.data.local.dao.CustomerApprovalDao1com.example.sharenshop.data.local.dao.CustomerDao0com.example.sharenshop.data.local.dao.InvoiceDao4com.example.sharenshop.data.local.dao.InvoiceItemDao0com.example.sharenshop.data.local.dao.MessageDao5com.example.sharenshop.data.local.dao.MessageStatsRaw:com.example.sharenshop.data.local.dao.ConversationStatsRaw6com.example.sharenshop.data.local.dao.MessageTypeCount:com.example.sharenshop.data.local.dao.MessagePriorityCount5com.example.sharenshop.data.local.dao.NotificationDao8com.example.sharenshop.data.local.dao.ProductCategoryDao0com.example.sharenshop.data.local.dao.ProductDao/com.example.sharenshop.data.local.dao.SellerDao-com.example.sharenshop.data.local.dao.UserDao6com.example.sharenshop.data.local.database.AppDatabase-com.example.sharenshop.data.model.AppSettings7com.example.sharenshop.data.model.AppSettings.Companion9com.example.sharenshop.data.model.AppSettings.$serializer*com.example.sharenshop.data.model.CartItem4com.example.sharenshop.data.model.CartItem.Companion6com.example.sharenshop.data.model.CartItem.$serializer1com.example.sharenshop.data.model.CartPaymentType;com.example.sharenshop.data.model.CartPaymentType.Companion-com.example.sharenshop.data.model.CartSummary1com.example.sharenshop.data.model.CartItemDetails.com.example.sharenshop.data.model.WishlistItem8com.example.sharenshop.data.model.WishlistItem.Companion:com.example.sharenshop.data.model.WishlistItem.$serializer6com.example.sharenshop.data.model.CartToInvoiceRequest3com.example.sharenshop.data.model.CreateInvoiceItem6com.example.sharenshop.data.model.CreateInvoiceRequest*com.example.sharenshop.data.model.Customer4com.example.sharenshop.data.model.Customer.Companion6com.example.sharenshop.data.model.Customer.$serializer9com.example.sharenshop.data.model.CustomerApprovalRequestCcom.example.sharenshop.data.model.CustomerApprovalRequest.CompanionEcom.example.sharenshop.data.model.CustomerApprovalRequest.$serializer0com.example.sharenshop.data.model.ApprovalStatus:<EMAIL>><EMAIL>.$serializer)com.example.sharenshop.data.model.Invoice3com.example.sharenshop.data.model.Invoice.Companion5com.example.sharenshop.data.model.Invoice.$serializer/com.example.sharenshop.data.model.InvoiceStatus9com.example.sharenshop.data.model.InvoiceStatus.Companion4com.example.sharenshop.data.model.InvoicePaymentType>com.example.sharenshop.data.model.InvoicePaymentType.Companion*com.example.sharenshop.data.model.SaleType4com.example.sharenshop.data.model.SaleType.Companion4com.example.sharenshop.data.model.InvoiceWithDetails8com.example.sharenshop.data.model.InvoiceItemWithProduct-com.example.sharenshop.data.model.InvoiceItem7com.example.sharenshop.data.model.InvoiceItem.Companion9com.example.sharenshop.data.model.InvoiceItem.$serializer*com.example.sharenshop.data.model.Language4com.example.sharenshop.data.model.Language.Companion)com.example.sharenshop.data.model.Message3com.example.sharenshop.data.model.Message.Companion5com.example.sharenshop.data.model.Message.$serializer-com.example.sharenshop.data.model.MessageType7com.example.sharenshop.data.model.MessageType.Companion1com.example.sharenshop.data.model.MessagePriority;com.example.sharenshop.data.model.MessagePriority.Companion.com.example.sharenshop.data.model.Conversation8com.example.sharenshop.data.model.Conversation.Companion:com.example.sharenshop.data.model.Conversation.$serializer.com.example.sharenshop.data.model.UserPresence8com.example.sharenshop.data.model.UserPresence.Companion:com.example.sharenshop.data.model.UserPresence.$serializer0com.example.sharenshop.data.model.PresenceStatus:com.example.sharenshop.data.model.PresenceStatus.Companion4com.example.sharenshop.data.model.MessageWithDetails9com.example.sharenshop.data.model.ConversationWithDetails4com.example.sharenshop.data.model.SendMessageRequest.com.example.sharenshop.data.model.MessageStats.com.example.sharenshop.data.model.ChatSettings8com.example.sharenshop.data.model.ChatSettings.Companion:com.example.sharenshop.data.model.ChatSettings.$serializer.com.example.sharenshop.data.model.Notification8com.example.sharenshop.data.model.Notification.Companion:com.example.sharenshop.data.model.Notification.$serializer2com.example.sharenshop.data.model.NotificationType<<EMAIL>@com.example.sharenshop.data.model.NotificationPriority.Companion6com.example.sharenshop.data.model.NotificationTemplate;<EMAIL>.$serializer(com.example.sharenshop.data.model.SmsLog2com.example.sharenshop.data.model.SmsLog.Companion4com.example.sharenshop.data.model.SmsLog.$serializer+com.example.sharenshop.data.model.SmsStatus5com.example.sharenshop.data.model.SmsStatus.Companion)com.example.sharenshop.data.model.Payment3com.example.sharenshop.data.model.Payment.Companion5com.example.sharenshop.data.model.Payment.$serializer/com.example.sharenshop.data.model.PaymentMethod9com.example.sharenshop.data.model.PaymentMethod.Companion/com.example.sharenshop.data.model.PaymentStatus9com.example.sharenshop.data.model.PaymentStatus.Companion0com.example.sharenshop.data.model.PaymentDetails,com.example.sharenshop.data.model.Permission1com.example.sharenshop.data.model.RolePermissions)com.example.sharenshop.data.model.Product3com.example.sharenshop.data.model.Product.Companion5com.example.sharenshop.data.model.Product.$serializer1com.example.sharenshop.data.model.ProductCategory;com.example.sharenshop.data.model.ProductCategory.Companion=com.example.sharenshop.data.model.ProductCategory.$serializer/com.example.sharenshop.data.model.StockMovement9com.example.sharenshop.data.model.StockMovement.Companion;com.example.sharenshop.data.model.StockMovement.$serializer3com.example.sharenshop.data.model.StockMovementType=com.example.sharenshop.data.model.StockMovementType.Companion4com.example.sharenshop.data.model.ProductStockReport0com.example.sharenshop.data.model.ProductVariant:com.example.sharenshop.data.model.ProductVariant.Companion<com.example.sharenshop.data.model.ProductVariant.$serializer2com.example.sharenshop.data.model.ProductInventory<com.example.sharenshop.data.model.ProductInventory.Companion>com.example.sharenshop.data.model.ProductInventory.$serializer0com.example.sharenshop.data.model.ColorInventory:com.example.sharenshop.data.model.ColorInventory.Companion<com.example.sharenshop.data.model.ColorInventory.$serializer/com.example.sharenshop.data.model.SizeInventory9com.example.sharenshop.data.model.SizeInventory.Companion;com.example.sharenshop.data.model.SizeInventory.$serializer.com.example.sharenshop.data.model.SizeQuantity8com.example.sharenshop.data.model.SizeQuantity.Companion:com.example.sharenshop.data.model.SizeQuantity.$serializer/com.example.sharenshop.data.model.ColorQuantity9com.example.sharenshop.data.model.ColorQuantity.Companion;com.example.sharenshop.data.model.ColorQuantity.$serializer5com.example.sharenshop.data.model.InventoryCalculator2com.example.sharenshop.data.model.ProductSalesInfo<com.example.sharenshop.data.model.ProductSalesInfo.Companion>com.example.sharenshop.data.model.ProductSalesInfo.$serializer(com.example.sharenshop.data.model.Seller2com.example.sharenshop.data.model.Seller.Companion4com.example.sharenshop.data.model.Seller.$serializer2com.example.sharenshop.data.model.SellerStatistics<com.example.sharenshop.data.model.SellerStatistics.Companion>com.example.sharenshop.data.model.SellerStatistics.$serializer3com.example.sharenshop.data.model.OverallStatistics=com.example.sharenshop.data.model.OverallStatistics.Companion?com.example.sharenshop.data.model.OverallStatistics.$serializer2com.example.sharenshop.data.model.SellerComparison<com.example.sharenshop.data.model.SellerComparison.Companion>com.example.sharenshop.data.model.SellerComparison.$<EMAIL>.$serializer8com.example.sharenshop.data.model.MonthlySalesStatisticsBcom.example.sharenshop.data.model.MonthlySalesStatistics.CompanionDcom.example.sharenshop.data.model.MonthlySalesStatistics.$serializer3com.example.sharenshop.data.model.TopSellingProduct=com.example.sharenshop.data.model.TopSellingProduct.Companion?com.example.sharenshop.data.model.TopSellingProduct.$serializer,com.example.sharenshop.data.model.Settlement6com.example.sharenshop.data.model.Settlement.Companion8com.example.sharenshop.data.model.Settlement.$serializer0com.example.sharenshop.data.model.SettlementType:com.example.sharenshop.data.model.SettlementType.Companion2com.example.sharenshop.data.model.SettlementStatus<com.example.sharenshop.data.model.SettlementStatus.Companion9com.example.sharenshop.data.model.SellerSettlementSummary3com.example.sharenshop.data.model.SettlementDetails1com.example.sharenshop.data.model.SalesStatistics;com.example.sharenshop.data.model.SalesStatistics.Companion=com.example.sharenshop.data.model.SalesStatistics.$serializer3com.example.sharenshop.data.model.SellersComparison=com.example.sharenshop.data.model.SellersComparison.Companion?com.example.sharenshop.data.model.SellersComparison.$serializer3com.example.sharenshop.data.model.SellerPerformance=com.example.sharenshop.data.model.SellerPerformance.Companion?com.example.sharenshop.data.model.SellerPerformance.$serializer-com.example.sharenshop.data.model.SellerBadge7com.example.sharenshop.data.model.SellerBadge.Companion.com.example.sharenshop.data.model.MonthlySales8com.example.sharenshop.data.model.MonthlySales.Companion:com.example.sharenshop.data.model.MonthlySales.$serializer,com.example.sharenshop.data.model.DailySales6com.example.sharenshop.data.model.DailySales.Companion8com.example.sharenshop.data.model.DailySales.$serializer3com.example.sharenshop.data.model.RecentTransaction=com.example.sharenshop.data.model.RecentTransaction.Companion?com.example.sharenshop.data.model.RecentTransaction.$serializer1com.example.sharenshop.data.model.SellerSalesData;com.example.sharenshop.data.model.SellerSalesData.Companion=com.example.sharenshop.data.model.SellerSalesData.$serializer2com.example.sharenshop.data.model.SellerGrowthData<com.example.sharenshop.data.model.SellerGrowthData.Companion>com.example.sharenshop.data.model.SellerGrowthData.$serializer-com.example.sharenshop.data.model.GrowthTrend7com.example.sharenshop.data.model.GrowthTrend.Companion4com.example.sharenshop.data.model.CustomerStatistics><EMAIL>.$serializer.com.example.sharenshop.data.model.LoyaltyLevel8com.example.sharenshop.data.model.LoyaltyLevel.Companion3com.example.sharenshop.data.model.ProductStatistics=com.example.sharenshop.data.model.ProductStatistics.Companion?com.example.sharenshop.data.model.ProductStatistics.$serializer5com.example.sharenshop.data.model.CategoryPerformance?com.example.sharenshop.data.model.CategoryPerformance.CompanionAcom.example.sharenshop.data.model.CategoryPerformance.$serializer5com.example.sharenshop.data.model.FinancialStatistics?com.example.sharenshop.data.model.FinancialStatistics.CompanionAcom.example.sharenshop.data.model.FinancialStatistics.$serializer1com.example.sharenshop.data.model.StatisticsAlert;com.example.sharenshop.data.model.StatisticsAlert.Companion=com.example.sharenshop.data.model.StatisticsAlert.$serializer,com.example.sharenshop.data.model.AlertLevel6com.example.sharenshop.data.model.AlertLevel.Companion(com.example.sharenshop.data.model.Report2com.example.sharenshop.data.model.Report.Companion4com.example.sharenshop.data.model.Report.$serializer.com.example.sharenshop.data.model.ReportFormat8com.example.sharenshop.data.model.ReportFormat.Companion.com.example.sharenshop.data.model.ReportOption8com.example.sharenshop.data.model.ReportOption.Companion:com.example.sharenshop.data.model.ReportOption.$serializer,com.example.sharenshop.data.model.TimePeriod6com.example.sharenshop.data.model.TimePeriod.Companion3com.example.sharenshop.data.model.StatisticsRequest=com.example.sharenshop.data.model.StatisticsRequest.Companion?com.example.sharenshop.data.model.StatisticsRequest.$serializer5com.example.sharenshop.data.model.DashboardStatistics?com.example.sharenshop.data.model.DashboardStatistics.CompanionAcom.example.sharenshop.data.model.DashboardStatistics.$serializer1com.example.sharenshop.data.model.SalesTrendPoint;com.example.sharenshop.data.model.SalesTrendPoint.Companion=com.example.sharenshop.data.model.SalesTrendPoint.$serializer+com.example.sharenshop.data.model.TimeRange:com.example.sharenshop.data.model.StatisticsRequestBuilder,com.example.sharenshop.data.model.ReportType6com.example.sharenshop.data.model.ReportType.Companion.com.example.sharenshop.data.model.ExportFormat8com.example.sharenshop.data.model.ExportFormat.Companion-com.example.sharenshop.data.model.TopCustomer7com.example.sharenshop.data.model.TopCustomer.Companion9com.example.sharenshop.data.model.TopCustomer.$serializer-com.example.sharenshop.data.model.Transaction7com.example.sharenshop.data.model.Transaction.Companion9com.example.sharenshop.data.model.Transaction.$serializer1com.example.sharenshop.data.model.TransactionType;com.example.sharenshop.data.model.TransactionType.Companion5com.example.sharenshop.data.model.TransactionCategory?com.example.sharenshop.data.model.TransactionCategory.Companion)com.example.sharenshop.data.model.Account3com.example.sharenshop.data.model.Account.Companion5com.example.sharenshop.data.model.Account.$serializer2com.example.sharenshop.data.model.AccountOwnerType<com.example.sharenshop.data.model.AccountOwnerType.Companion-com.example.sharenshop.data.model.AccountType7com.example.sharenshop.data.model.AccountType.Companion1com.example.sharenshop.data.model.FinancialReport*com.example.sharenshop.data.model.UserRole4com.example.sharenshop.data.model.UserRole.Companion&com.example.sharenshop.data.model.User0com.example.sharenshop.data.model.User.Companion2com.example.sharenshop.data.model.User.$serializer1com.example.sharenshop.data.remote.SupabaseClient1com.example.sharenshop.data.remote.SupabaseTables4com.example.sharenshop.data.remote.SupabaseConstantsKcom.example.sharenshop.data.remote.repository.MockSupabaseInvoiceRepositoryKcom.example.sharenshop.data.remote.repository.MockSupabaseProductRepositoryHcom.example.sharenshop.data.remote.repository.MockSupabaseUserRepository9com.example.sharenshop.data.repository.CartRepositoryImplEcom.example.sharenshop.data.repository.CustomerApprovalRepositoryImpl=com.example.sharenshop.data.repository.CustomerRepositoryImpl<com.example.sharenshop.data.repository.InvoiceRepositoryImpl<com.example.sharenshop.data.repository.MessageRepositoryImplAcom.example.sharenshop.data.repository.NotificationRepositoryImpl8com.example.sharenshop.data.repository.ProductRepository<com.example.sharenshop.data.repository.ProductRepositoryImpl=com.example.sharenshop.data.repository.SecurityRepositoryImpl;com.example.sharenshop.data.repository.SellerRepositoryImpl=com.example.sharenshop.data.repository.SettingsRepositoryImpl5com.example.sharenshop.data.repository.DashboardStats;com.example.sharenshop.data.repository.SharenShopRepository?com.example.sharenshop.data.repository.SharenShopRepositoryImpl?com.example.sharenshop.data.repository.StatisticsRepositoryImpl9com.example.sharenshop.data.repository.SupabaseRepository9com.example.sharenshop.data.repository.UserRepositoryImpl9com.example.sharenshop.data.repository.VariableRepository>com.example.sharenshop.data.security.DatabaseEncryptionService6com.example.sharenshop.data.security.EncryptionService:com.example.sharenshop.data.security.FileEncryptionServiceGcom.example.sharenshop.data.security.SharedPreferencesEncryptionService2com.example.sharenshop.data.session.SessionManager<com.example.sharenshop.data.session.SessionManager.Companion#com.example.sharenshop.di.DaoModule(com.example.sharenshop.di.DatabaseModule'com.example.sharenshop.di.NetworkModule*com.example.sharenshop.di.RepositoryModule4com.example.sharenshop.di.RepositoryModule.Companion(com.example.sharenshop.di.SecurityModule2com.example.sharenshop.di.SecurityModule.Companion(com.example.sharenshop.di.SupabaseModule'com.example.sharenshop.di.UseCaseModule7com.example.sharenshop.domain.repository.CartRepositoryCcom.example.sharenshop.domain.repository.CustomerApprovalRepository;com.example.sharenshop.domain.repository.CustomerRepository:com.example.sharenshop.domain.repository.InvoiceRepository:com.example.sharenshop.domain.repository.MessageRepository;com.example.sharenshop.domain.repository.ChatActivityReport8com.example.sharenshop.domain.repository.ContactActivity5com.example.sharenshop.domain.repository.ExportFormat?com.example.sharenshop.domain.repository.NotificationRepository:com.example.sharenshop.domain.repository.ProductRepository;com.example.sharenshop.domain.repository.SecurityRepository9com.example.sharenshop.domain.repository.SellerRepository;com.example.sharenshop.domain.repository.SettingsRepository7com.example.sharenshop.domain.repository.DashboardStats=com.example.sharenshop.domain.repository.SharenShopRepository=com.example.sharenshop.domain.repository.StatisticsRepository7com.example.sharenshop.domain.repository.UserRepository3com.example.sharenshop.domain.use_case.CartUseCases5com.example.sharenshop.domain.use_case.GetCartUseCase7com.example.sharenshop.domain.use_case.AddToCartUseCaseDcom.example.sharenshop.domain.use_case.UpdateCartItemQuantityUseCase<com.example.sharenshop.domain.use_case.RemoveFromCartUseCase7com.example.sharenshop.domain.use_case.ClearCartUseCase?com.example.sharenshop.domain.use_case.CustomerApprovalUseCases<com.example.sharenshop.domain.use_case.SubmitApprovalRequest6com.example.sharenshop.domain.use_case.ApproveCustomer5com.example.sharenshop.domain.use_case.RejectCustomer9com.example.sharenshop.domain.use_case.GetPendingRequests<com.example.sharenshop.domain.use_case.GetRequestsByReferrer;com.example.sharenshop.domain.use_case.ValidateReferrerCode7com.example.sharenshop.domain.use_case.CustomerUseCases2com.example.sharenshop.domain.use_case.GetCustomer5com.example.sharenshop.domain.use_case.InsertCustomer5com.example.sharenshop.domain.use_case.UpdateCustomer5com.example.sharenshop.domain.use_case.DeleteCustomer6com.example.sharenshop.domain.use_case.GetAllCustomers6com.example.sharenshop.domain.use_case.SearchCustomers5com.example.sharenshop.domain.use_case.GetUsersByType6com.example.sharenshop.domain.use_case.InvoiceUseCases1com.example.sharenshop.domain.use_case.GetInvoice4com.example.sharenshop.domain.use_case.InsertInvoice4com.example.sharenshop.domain.use_case.UpdateInvoice4com.example.sharenshop.domain.use_case.DeleteInvoice5com.example.sharenshop.domain.use_case.GetAllInvoices<com.example.sharenshop.domain.use_case.GetInvoicesByCustomer8com.example.sharenshop.domain.use_case.GetInvoicesByUser6com.example.sharenshop.domain.use_case.ProductUseCases1com.example.sharenshop.domain.use_case.GetProduct4com.example.sharenshop.domain.use_case.InsertProduct4com.example.sharenshop.domain.use_case.UpdateProduct4com.example.sharenshop.domain.use_case.DeleteProduct5com.example.sharenshop.domain.use_case.GetAllProducts5com.example.sharenshop.domain.use_case.SearchProducts9com.example.sharenshop.domain.use_case.UploadProductImage7com.example.sharenshop.domain.use_case.GetAllCategories7com.example.sharenshop.domain.use_case.SecurityUseCases<com.example.sharenshop.domain.use_case.GetEncryptionSettings=com.example.sharenshop.domain.use_case.SaveEncryptionSettings2com.example.sharenshop.domain.use_case.EncryptData2com.example.sharenshop.domain.use_case.DecryptData5com.example.sharenshop.domain.use_case.SellerUseCases0com.example.sharenshop.domain.use_case.GetSeller3com.example.sharenshop.domain.use_case.InsertSeller3com.example.sharenshop.domain.use_case.UpdateSeller3com.example.sharenshop.domain.use_case.DeleteSeller4com.example.sharenshop.domain.use_case.GetAllSellers4com.example.sharenshop.domain.use_case.SearchSellers7com.example.sharenshop.domain.use_case.SettingsUseCases5com.example.sharenshop.domain.use_case.GetAppSettings6com.example.sharenshop.domain.use_case.SaveAppSettings9com.example.sharenshop.domain.use_case.StatisticsUseCases:com.example.sharenshop.domain.use_case.GetTotalSalesAmount;com.example.sharenshop.domain.use_case.GetTotalProductsSold<com.example.sharenshop.domain.use_case.GetTopSellingProducts:com.example.sharenshop.domain.use_case.GetCustomerSpending>com.example.sharenshop.domain.use_case.GetSalesByPaymentStatus;com.example.sharenshop.domain.use_case.GetSalesByTimePeriod=com.example.sharenshop.domain.use_case.GetDashboardStatistics9com.example.sharenshop.domain.use_case.GetSalesStatistics:com.example.sharenshop.domain.use_case.GetSellerStatistics;com.example.sharenshop.domain.use_case.GetSellersComparison<com.example.sharenshop.domain.use_case.GetCustomerStatistics;com.example.sharenshop.domain.use_case.GetProductStatistics=com.example.sharenshop.domain.use_case.GetFinancialStatistics<com.example.sharenshop.domain.use_case.GetRealTimeStatistics<com.example.sharenshop.domain.use_case.GetRecentTransactions:com.example.sharenshop.domain.use_case.GetStatisticsAlerts3com.example.sharenshop.domain.use_case.ExportReport8com.example.sharenshop.domain.use_case.RefreshStatistics3com.example.sharenshop.domain.use_case.ExportFormat3com.example.sharenshop.domain.use_case.UserUseCases.com.example.sharenshop.domain.use_case.GetUser1com.example.sharenshop.domain.use_case.InsertUser1com.example.sharenshop.domain.use_case.UpdateUser1com.example.sharenshop.domain.use_case.DeleteUser2com.example.sharenshop.domain.use_case.GetAllUsers9com.example.sharenshop.domain.use_case.GetUsersByUserType8com.example.sharenshop.domain.use_case.GetUserByUsername5com.example.sharenshop.domain.use_case.GetUserByEmail1com.example.sharenshop.domain.use_case.GetSellersBcom.example.sharenshop.presentation.addproduct.AddProductViewModel6com.example.sharenshop.presentation.auth.AuthViewModel6com.example.sharenshop.presentation.cart.CartViewModel>com.example.sharenshop.presentation.customer.CustomerViewModelOcom.example.sharenshop.presentation.customer_approval.CustomerApprovalViewModel6com.example.sharenshop.presentation.home.HomeViewModel<com.example.sharenshop.presentation.invoice.InvoiceViewModel6com.example.sharenshop.presentation.main.MainViewModelFcom.example.sharenshop.presentation.notification.NotificationViewModelDcom.example.sharenshop.presentation.notification.NotificationUiStateCcom.example.sharenshop.presentation.notification.NotificationHelper<com.example.sharenshop.presentation.product.ProductViewModel:<EMAIL>.user_management.UserManagementViewModelScom.example.sharenshop.presentation.user_management.UserManagementViewModel.UiState?com.example.sharenshop.presentation.variables.VariableViewModel2com.example.sharenshop.ui.components.SalesCartItem+com.example.sharenshop.ui.navigation.Screen0com.example.sharenshop.ui.navigation.Screen.Auth0com.example.sharenshop.ui.navigation.Screen.Main0com.example.sharenshop.ui.navigation.Screen.Home3com.example.sharenshop.ui.navigation.Screen.Invoice3com.example.sharenshop.ui.navigation.Screen.Product2com.example.sharenshop.ui.navigation.Screen.Seller6com.example.sharenshop.ui.navigation.Screen.Statistics:com.example.sharenshop.ui.navigation.Screen.UserManagement3com.example.sharenshop.ui.navigation.Screen.Payment6com.example.sharenshop.ui.navigation.Screen.Settlement6com.example.sharenshop.ui.navigation.Screen.Accounting;com.example.sharenshop.ui.navigation.Screen.PendingApproval5com.example.sharenshop.ui.navigation.Screen.Variables>com.example.sharenshop.ui.navigation.Screen.VariableManagement=com.example.sharenshop.ui.navigation.Screen.ProductManagement7com.example.sharenshop.ui.navigation.Screen.AllProducts6com.example.sharenshop.ui.navigation.Screen.AddProduct;com.example.sharenshop.ui.navigation.Screen.AddProductStep2;com.example.sharenshop.ui.navigation.Screen.AddProductStep38com.example.sharenshop.ui.navigation.Screen.ProductStats:com.example.sharenshop.ui.navigation.Screen.SellerPayments9com.example.sharenshop.ui.navigation.Screen.SellerDetails<com.example.sharenshop.ui.navigation.Screen.SellerManagement>com.example.sharenshop.ui.navigation.Screen.SellerDetailedInfo>com.example.sharenshop.ui.navigation.Screen.CustomerManagement;com.example.sharenshop.ui.navigation.Screen.CustomerDetails4com.example.sharenshop.ui.navigation.Screen.Messages7com.example.sharenshop.ui.navigation.Screen.SellerStats;com.example.sharenshop.ui.navigation.Screen.SellerCustomersAcom.example.sharenshop.ui.navigation.Screen.SellerCustomerDetails7com.example.sharenshop.ui.navigation.Screen.SellProduct?com.example.sharenshop.ui.navigation.Screen.InvoiceConfirmation=com.example.sharenshop.ui.navigation.Screen.PaymentManagement5com.example.sharenshop.ui.preview.IconPreviewActivity2com.example.sharenshop.ui.screens.ProductStep1Data2com.example.sharenshop.ui.screens.ProductStep2Data2com.example.sharenshop.ui.screens.ProductStep3Data-com.example.sharenshop.ui.screens.ProductItem-com.example.sharenshop.ui.screens.ChatMessage.com.example.sharenshop.ui.screens.CustomerInfo-com.example.sharenshop.ui.screens.InvoiceInfo/com.example.sharenshop.ui.screens.InvoiceStatus-com.example.sharenshop.ui.screens.InvoiceItem+com.example.sharenshop.ui.screens.StoryItem9com.example.sharenshop.ui.screens.InvoiceConfirmationData*com.example.sharenshop.ui.screens.MenuItem/com.example.sharenshop.ui.screens.MessageStatus0com.example.sharenshop.ui.screens.PaymentMessage)com.example.sharenshop.ui.screens.TabInfo-com.example.sharenshop.ui.screens.PaymentType8com.example.sharenshop.ui.screens.SellerFinancialSummary0com.example.sharenshop.ui.screens.DebtorCustomer/com.example.sharenshop.ui.screens.PaymentRecord2com.example.sharenshop.ui.screens.ProductWithStats2com.example.sharenshop.ui.screens.SalesInvoiceData/com.example.sharenshop.ui.screens.SalesCartItem-com.example.sharenshop.ui.screens.SaleProduct0com.example.sharenshop.ui.screens.ProductVariant.com.example.sharenshop.ui.screens.SaleCustomer*com.example.sharenshop.ui.screens.CartItem*com.example.sharenshop.ui.screens.SaleData5com.example.sharenshop.ui.screens.SellerInvoiceStatus3com.example.sharenshop.ui.screens.SellerInvoiceInfo3com.example.sharenshop.ui.screens.SellerInvoiceItem4com.example.sharenshop.ui.screens.SellerCustomerInfo6com.example.sharenshop.ui.screens.SellerManagementInfo,com.example.sharenshop.ui.screens.SellerInfo5com.example.sharenshop.ui.screens.SellerPersonalStats+com.example.sharenshop.ui.screens.AppScreen,com.example.sharenshop.ui.screens.SortOption+com.example.sharenshop.ui.screens.OrderData2com.example.sharenshop.ui.screens.ShoppingCartItem.com.example.sharenshop.ui.screens.VariableItem.com.example.sharenshop.ui.screens.VariableType2com.example.sharenshop.ui.screens.VariableTypeEnum0com.example.sharenshop.ui.theme.SharenShopColors+com.example.sharenshop.ui.theme.ColorHelper-com.example.sharenshop.utils.AppIconGenerator-com.example.sharenshop.utils.PermissionHelper/com.example.sharenshop.utils.SupabaseTestHelper"com.example.sharenshop.BuildConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             
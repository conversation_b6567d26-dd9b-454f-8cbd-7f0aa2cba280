// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.local.dao.CartDao;
import com.example.sharenshop.data.local.database.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaoModule_ProvideCartDaoFactory implements Factory<CartDao> {
  private final Provider<AppDatabase> appDatabaseProvider;

  public DaoModule_ProvideCartDaoFactory(Provider<AppDatabase> appDatabaseProvider) {
    this.appDatabaseProvider = appDatabaseProvider;
  }

  @Override
  public CartDao get() {
    return provideCartDao(appDatabaseProvider.get());
  }

  public static DaoModule_ProvideCartDaoFactory create(Provider<AppDatabase> appDatabaseProvider) {
    return new DaoModule_ProvideCartDaoFactory(appDatabaseProvider);
  }

  public static CartDao provideCartDao(AppDatabase appDatabase) {
    return Preconditions.checkNotNullFromProvides(DaoModule.INSTANCE.provideCartDao(appDatabase));
  }
}

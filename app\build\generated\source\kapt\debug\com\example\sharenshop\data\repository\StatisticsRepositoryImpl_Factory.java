// Generated by Da<PERSON> (https://dagger.dev).
package com.example.sharenshop.data.repository;

import com.example.sharenshop.domain.repository.InvoiceRepository;
import com.example.sharenshop.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class StatisticsRepositoryImpl_Factory implements Factory<StatisticsRepositoryImpl> {
  private final Provider<InvoiceRepository> invoiceRepositoryProvider;

  private final Provider<ProductRepository> productRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  public StatisticsRepositoryImpl_Factory(Provider<InvoiceRepository> invoiceRepositoryProvider,
      Provider<ProductRepository> productRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    this.invoiceRepositoryProvider = invoiceRepositoryProvider;
    this.productRepositoryProvider = productRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public StatisticsRepositoryImpl get() {
    return newInstance(invoiceRepositoryProvider.get(), productRepositoryProvider.get(), userRepositoryProvider.get());
  }

  public static StatisticsRepositoryImpl_Factory create(
      Provider<InvoiceRepository> invoiceRepositoryProvider,
      Provider<ProductRepository> productRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    return new StatisticsRepositoryImpl_Factory(invoiceRepositoryProvider, productRepositoryProvider, userRepositoryProvider);
  }

  public static StatisticsRepositoryImpl newInstance(InvoiceRepository invoiceRepository,
      ProductRepository productRepository, UserRepository userRepository) {
    return new StatisticsRepositoryImpl(invoiceRepository, productRepository, userRepository);
  }
}

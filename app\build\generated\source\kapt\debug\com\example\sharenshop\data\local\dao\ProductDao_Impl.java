package com.example.sharenshop.data.local.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.sharenshop.data.local.converter.BigDecimalConverter;
import com.example.sharenshop.data.local.converters.TypeConverters;
import com.example.sharenshop.data.model.Product;
import com.example.sharenshop.data.model.ProductVariant;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class ProductDao_Impl implements ProductDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Product> __insertionAdapterOfProduct;

  private final BigDecimalConverter __bigDecimalConverter = new BigDecimalConverter();

  private final TypeConverters __typeConverters = new TypeConverters();

  private final EntityDeletionOrUpdateAdapter<Product> __updateAdapterOfProduct;

  private final SharedSQLiteStatement __preparedStmtOfDeleteProductById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllProducts;

  public ProductDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfProduct = new EntityInsertionAdapter<Product>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `products` (`id`,`productCode`,`name`,`description`,`categoryId`,`subCategoryId`,`purchasePrice`,`salePrice`,`creditPrice`,`totalStock`,`availableStock`,`reservedStock`,`minStockLevel`,`colors`,`sizes`,`variants`,`imageUrls`,`thumbnailUrl`,`isActive`,`isVisible`,`isFeatured`,`createdBy`,`createdAt`,`updatedAt`,`lastStockUpdate`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Product entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getProductCode() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getProductCode());
        }
        if (entity.getName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getName());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDescription());
        }
        if (entity.getCategoryId() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getCategoryId());
        }
        if (entity.getSubCategoryId() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getSubCategoryId());
        }
        final String _tmp = __bigDecimalConverter.fromBigDecimal(entity.getPurchasePrice());
        if (_tmp == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp);
        }
        final String _tmp_1 = __bigDecimalConverter.fromBigDecimal(entity.getSalePrice());
        if (_tmp_1 == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp_1);
        }
        final String _tmp_2 = __bigDecimalConverter.fromBigDecimal(entity.getCreditPrice());
        if (_tmp_2 == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp_2);
        }
        statement.bindLong(10, entity.getTotalStock());
        statement.bindLong(11, entity.getAvailableStock());
        statement.bindLong(12, entity.getReservedStock());
        statement.bindLong(13, entity.getMinStockLevel());
        final String _tmp_3 = __typeConverters.fromStringList(entity.getColors());
        if (_tmp_3 == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, _tmp_3);
        }
        final String _tmp_4 = __typeConverters.fromStringList(entity.getSizes());
        if (_tmp_4 == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, _tmp_4);
        }
        final String _tmp_5 = __typeConverters.fromProductVariantList(entity.getVariants());
        if (_tmp_5 == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, _tmp_5);
        }
        final String _tmp_6 = __typeConverters.fromStringList(entity.getImageUrls());
        if (_tmp_6 == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, _tmp_6);
        }
        if (entity.getThumbnailUrl() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getThumbnailUrl());
        }
        final int _tmp_7 = entity.isActive() ? 1 : 0;
        statement.bindLong(19, _tmp_7);
        final int _tmp_8 = entity.isVisible() ? 1 : 0;
        statement.bindLong(20, _tmp_8);
        final int _tmp_9 = entity.isFeatured() ? 1 : 0;
        statement.bindLong(21, _tmp_9);
        if (entity.getCreatedBy() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getCreatedBy());
        }
        statement.bindLong(23, entity.getCreatedAt());
        if (entity.getUpdatedAt() == null) {
          statement.bindNull(24);
        } else {
          statement.bindLong(24, entity.getUpdatedAt());
        }
        if (entity.getLastStockUpdate() == null) {
          statement.bindNull(25);
        } else {
          statement.bindLong(25, entity.getLastStockUpdate());
        }
      }
    };
    this.__updateAdapterOfProduct = new EntityDeletionOrUpdateAdapter<Product>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `products` SET `id` = ?,`productCode` = ?,`name` = ?,`description` = ?,`categoryId` = ?,`subCategoryId` = ?,`purchasePrice` = ?,`salePrice` = ?,`creditPrice` = ?,`totalStock` = ?,`availableStock` = ?,`reservedStock` = ?,`minStockLevel` = ?,`colors` = ?,`sizes` = ?,`variants` = ?,`imageUrls` = ?,`thumbnailUrl` = ?,`isActive` = ?,`isVisible` = ?,`isFeatured` = ?,`createdBy` = ?,`createdAt` = ?,`updatedAt` = ?,`lastStockUpdate` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Product entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getProductCode() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getProductCode());
        }
        if (entity.getName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getName());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDescription());
        }
        if (entity.getCategoryId() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getCategoryId());
        }
        if (entity.getSubCategoryId() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getSubCategoryId());
        }
        final String _tmp = __bigDecimalConverter.fromBigDecimal(entity.getPurchasePrice());
        if (_tmp == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp);
        }
        final String _tmp_1 = __bigDecimalConverter.fromBigDecimal(entity.getSalePrice());
        if (_tmp_1 == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp_1);
        }
        final String _tmp_2 = __bigDecimalConverter.fromBigDecimal(entity.getCreditPrice());
        if (_tmp_2 == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp_2);
        }
        statement.bindLong(10, entity.getTotalStock());
        statement.bindLong(11, entity.getAvailableStock());
        statement.bindLong(12, entity.getReservedStock());
        statement.bindLong(13, entity.getMinStockLevel());
        final String _tmp_3 = __typeConverters.fromStringList(entity.getColors());
        if (_tmp_3 == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, _tmp_3);
        }
        final String _tmp_4 = __typeConverters.fromStringList(entity.getSizes());
        if (_tmp_4 == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, _tmp_4);
        }
        final String _tmp_5 = __typeConverters.fromProductVariantList(entity.getVariants());
        if (_tmp_5 == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, _tmp_5);
        }
        final String _tmp_6 = __typeConverters.fromStringList(entity.getImageUrls());
        if (_tmp_6 == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, _tmp_6);
        }
        if (entity.getThumbnailUrl() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getThumbnailUrl());
        }
        final int _tmp_7 = entity.isActive() ? 1 : 0;
        statement.bindLong(19, _tmp_7);
        final int _tmp_8 = entity.isVisible() ? 1 : 0;
        statement.bindLong(20, _tmp_8);
        final int _tmp_9 = entity.isFeatured() ? 1 : 0;
        statement.bindLong(21, _tmp_9);
        if (entity.getCreatedBy() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getCreatedBy());
        }
        statement.bindLong(23, entity.getCreatedAt());
        if (entity.getUpdatedAt() == null) {
          statement.bindNull(24);
        } else {
          statement.bindLong(24, entity.getUpdatedAt());
        }
        if (entity.getLastStockUpdate() == null) {
          statement.bindNull(25);
        } else {
          statement.bindLong(25, entity.getLastStockUpdate());
        }
        if (entity.getId() == null) {
          statement.bindNull(26);
        } else {
          statement.bindString(26, entity.getId());
        }
      }
    };
    this.__preparedStmtOfDeleteProductById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM products WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllProducts = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM products";
        return _query;
      }
    };
  }

  @Override
  public Object insertProduct(final Product product, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfProduct.insert(product);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertAll(final List<Product> products,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfProduct.insert(products);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateProduct(final Product product, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfProduct.handle(product);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteProductById(final String productId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteProductById.acquire();
        int _argIndex = 1;
        if (productId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, productId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteProductById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllProducts(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllProducts.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllProducts.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<Product> getProductById(final String productId) {
    final String _sql = "SELECT * FROM products WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (productId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, productId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"products"}, new Callable<Product>() {
      @Override
      @Nullable
      public Product call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfProductCode = CursorUtil.getColumnIndexOrThrow(_cursor, "productCode");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfSubCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subCategoryId");
          final int _cursorIndexOfPurchasePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "purchasePrice");
          final int _cursorIndexOfSalePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "salePrice");
          final int _cursorIndexOfCreditPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "creditPrice");
          final int _cursorIndexOfTotalStock = CursorUtil.getColumnIndexOrThrow(_cursor, "totalStock");
          final int _cursorIndexOfAvailableStock = CursorUtil.getColumnIndexOrThrow(_cursor, "availableStock");
          final int _cursorIndexOfReservedStock = CursorUtil.getColumnIndexOrThrow(_cursor, "reservedStock");
          final int _cursorIndexOfMinStockLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "minStockLevel");
          final int _cursorIndexOfColors = CursorUtil.getColumnIndexOrThrow(_cursor, "colors");
          final int _cursorIndexOfSizes = CursorUtil.getColumnIndexOrThrow(_cursor, "sizes");
          final int _cursorIndexOfVariants = CursorUtil.getColumnIndexOrThrow(_cursor, "variants");
          final int _cursorIndexOfImageUrls = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUrls");
          final int _cursorIndexOfThumbnailUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailUrl");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfIsVisible = CursorUtil.getColumnIndexOrThrow(_cursor, "isVisible");
          final int _cursorIndexOfIsFeatured = CursorUtil.getColumnIndexOrThrow(_cursor, "isFeatured");
          final int _cursorIndexOfCreatedBy = CursorUtil.getColumnIndexOrThrow(_cursor, "createdBy");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfLastStockUpdate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStockUpdate");
          final Product _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpProductCode;
            if (_cursor.isNull(_cursorIndexOfProductCode)) {
              _tmpProductCode = null;
            } else {
              _tmpProductCode = _cursor.getString(_cursorIndexOfProductCode);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpCategoryId;
            if (_cursor.isNull(_cursorIndexOfCategoryId)) {
              _tmpCategoryId = null;
            } else {
              _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            }
            final String _tmpSubCategoryId;
            if (_cursor.isNull(_cursorIndexOfSubCategoryId)) {
              _tmpSubCategoryId = null;
            } else {
              _tmpSubCategoryId = _cursor.getString(_cursorIndexOfSubCategoryId);
            }
            final BigDecimal _tmpPurchasePrice;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfPurchasePrice)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfPurchasePrice);
            }
            _tmpPurchasePrice = __bigDecimalConverter.toBigDecimal(_tmp);
            final BigDecimal _tmpSalePrice;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfSalePrice)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfSalePrice);
            }
            _tmpSalePrice = __bigDecimalConverter.toBigDecimal(_tmp_1);
            final BigDecimal _tmpCreditPrice;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreditPrice)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfCreditPrice);
            }
            _tmpCreditPrice = __bigDecimalConverter.toBigDecimal(_tmp_2);
            final int _tmpTotalStock;
            _tmpTotalStock = _cursor.getInt(_cursorIndexOfTotalStock);
            final int _tmpAvailableStock;
            _tmpAvailableStock = _cursor.getInt(_cursorIndexOfAvailableStock);
            final int _tmpReservedStock;
            _tmpReservedStock = _cursor.getInt(_cursorIndexOfReservedStock);
            final int _tmpMinStockLevel;
            _tmpMinStockLevel = _cursor.getInt(_cursorIndexOfMinStockLevel);
            final List<String> _tmpColors;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfColors)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfColors);
            }
            _tmpColors = __typeConverters.toStringList(_tmp_3);
            final List<String> _tmpSizes;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfSizes)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfSizes);
            }
            _tmpSizes = __typeConverters.toStringList(_tmp_4);
            final List<ProductVariant> _tmpVariants;
            final String _tmp_5;
            if (_cursor.isNull(_cursorIndexOfVariants)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getString(_cursorIndexOfVariants);
            }
            _tmpVariants = __typeConverters.toProductVariantList(_tmp_5);
            final List<String> _tmpImageUrls;
            final String _tmp_6;
            if (_cursor.isNull(_cursorIndexOfImageUrls)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getString(_cursorIndexOfImageUrls);
            }
            _tmpImageUrls = __typeConverters.toStringList(_tmp_6);
            final String _tmpThumbnailUrl;
            if (_cursor.isNull(_cursorIndexOfThumbnailUrl)) {
              _tmpThumbnailUrl = null;
            } else {
              _tmpThumbnailUrl = _cursor.getString(_cursorIndexOfThumbnailUrl);
            }
            final boolean _tmpIsActive;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_7 != 0;
            final boolean _tmpIsVisible;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfIsVisible);
            _tmpIsVisible = _tmp_8 != 0;
            final boolean _tmpIsFeatured;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfIsFeatured);
            _tmpIsFeatured = _tmp_9 != 0;
            final String _tmpCreatedBy;
            if (_cursor.isNull(_cursorIndexOfCreatedBy)) {
              _tmpCreatedBy = null;
            } else {
              _tmpCreatedBy = _cursor.getString(_cursorIndexOfCreatedBy);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpUpdatedAt;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmpUpdatedAt = null;
            } else {
              _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Long _tmpLastStockUpdate;
            if (_cursor.isNull(_cursorIndexOfLastStockUpdate)) {
              _tmpLastStockUpdate = null;
            } else {
              _tmpLastStockUpdate = _cursor.getLong(_cursorIndexOfLastStockUpdate);
            }
            _result = new Product(_tmpId,_tmpProductCode,_tmpName,_tmpDescription,_tmpCategoryId,_tmpSubCategoryId,_tmpPurchasePrice,_tmpSalePrice,_tmpCreditPrice,_tmpTotalStock,_tmpAvailableStock,_tmpReservedStock,_tmpMinStockLevel,_tmpColors,_tmpSizes,_tmpVariants,_tmpImageUrls,_tmpThumbnailUrl,_tmpIsActive,_tmpIsVisible,_tmpIsFeatured,_tmpCreatedBy,_tmpCreatedAt,_tmpUpdatedAt,_tmpLastStockUpdate);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Product>> getAllProducts() {
    final String _sql = "SELECT * FROM products";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"products"}, new Callable<List<Product>>() {
      @Override
      @NonNull
      public List<Product> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfProductCode = CursorUtil.getColumnIndexOrThrow(_cursor, "productCode");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfSubCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subCategoryId");
          final int _cursorIndexOfPurchasePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "purchasePrice");
          final int _cursorIndexOfSalePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "salePrice");
          final int _cursorIndexOfCreditPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "creditPrice");
          final int _cursorIndexOfTotalStock = CursorUtil.getColumnIndexOrThrow(_cursor, "totalStock");
          final int _cursorIndexOfAvailableStock = CursorUtil.getColumnIndexOrThrow(_cursor, "availableStock");
          final int _cursorIndexOfReservedStock = CursorUtil.getColumnIndexOrThrow(_cursor, "reservedStock");
          final int _cursorIndexOfMinStockLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "minStockLevel");
          final int _cursorIndexOfColors = CursorUtil.getColumnIndexOrThrow(_cursor, "colors");
          final int _cursorIndexOfSizes = CursorUtil.getColumnIndexOrThrow(_cursor, "sizes");
          final int _cursorIndexOfVariants = CursorUtil.getColumnIndexOrThrow(_cursor, "variants");
          final int _cursorIndexOfImageUrls = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUrls");
          final int _cursorIndexOfThumbnailUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailUrl");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfIsVisible = CursorUtil.getColumnIndexOrThrow(_cursor, "isVisible");
          final int _cursorIndexOfIsFeatured = CursorUtil.getColumnIndexOrThrow(_cursor, "isFeatured");
          final int _cursorIndexOfCreatedBy = CursorUtil.getColumnIndexOrThrow(_cursor, "createdBy");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfLastStockUpdate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStockUpdate");
          final List<Product> _result = new ArrayList<Product>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Product _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpProductCode;
            if (_cursor.isNull(_cursorIndexOfProductCode)) {
              _tmpProductCode = null;
            } else {
              _tmpProductCode = _cursor.getString(_cursorIndexOfProductCode);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpCategoryId;
            if (_cursor.isNull(_cursorIndexOfCategoryId)) {
              _tmpCategoryId = null;
            } else {
              _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            }
            final String _tmpSubCategoryId;
            if (_cursor.isNull(_cursorIndexOfSubCategoryId)) {
              _tmpSubCategoryId = null;
            } else {
              _tmpSubCategoryId = _cursor.getString(_cursorIndexOfSubCategoryId);
            }
            final BigDecimal _tmpPurchasePrice;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfPurchasePrice)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfPurchasePrice);
            }
            _tmpPurchasePrice = __bigDecimalConverter.toBigDecimal(_tmp);
            final BigDecimal _tmpSalePrice;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfSalePrice)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfSalePrice);
            }
            _tmpSalePrice = __bigDecimalConverter.toBigDecimal(_tmp_1);
            final BigDecimal _tmpCreditPrice;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreditPrice)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfCreditPrice);
            }
            _tmpCreditPrice = __bigDecimalConverter.toBigDecimal(_tmp_2);
            final int _tmpTotalStock;
            _tmpTotalStock = _cursor.getInt(_cursorIndexOfTotalStock);
            final int _tmpAvailableStock;
            _tmpAvailableStock = _cursor.getInt(_cursorIndexOfAvailableStock);
            final int _tmpReservedStock;
            _tmpReservedStock = _cursor.getInt(_cursorIndexOfReservedStock);
            final int _tmpMinStockLevel;
            _tmpMinStockLevel = _cursor.getInt(_cursorIndexOfMinStockLevel);
            final List<String> _tmpColors;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfColors)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfColors);
            }
            _tmpColors = __typeConverters.toStringList(_tmp_3);
            final List<String> _tmpSizes;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfSizes)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfSizes);
            }
            _tmpSizes = __typeConverters.toStringList(_tmp_4);
            final List<ProductVariant> _tmpVariants;
            final String _tmp_5;
            if (_cursor.isNull(_cursorIndexOfVariants)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getString(_cursorIndexOfVariants);
            }
            _tmpVariants = __typeConverters.toProductVariantList(_tmp_5);
            final List<String> _tmpImageUrls;
            final String _tmp_6;
            if (_cursor.isNull(_cursorIndexOfImageUrls)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getString(_cursorIndexOfImageUrls);
            }
            _tmpImageUrls = __typeConverters.toStringList(_tmp_6);
            final String _tmpThumbnailUrl;
            if (_cursor.isNull(_cursorIndexOfThumbnailUrl)) {
              _tmpThumbnailUrl = null;
            } else {
              _tmpThumbnailUrl = _cursor.getString(_cursorIndexOfThumbnailUrl);
            }
            final boolean _tmpIsActive;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_7 != 0;
            final boolean _tmpIsVisible;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfIsVisible);
            _tmpIsVisible = _tmp_8 != 0;
            final boolean _tmpIsFeatured;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfIsFeatured);
            _tmpIsFeatured = _tmp_9 != 0;
            final String _tmpCreatedBy;
            if (_cursor.isNull(_cursorIndexOfCreatedBy)) {
              _tmpCreatedBy = null;
            } else {
              _tmpCreatedBy = _cursor.getString(_cursorIndexOfCreatedBy);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpUpdatedAt;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmpUpdatedAt = null;
            } else {
              _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Long _tmpLastStockUpdate;
            if (_cursor.isNull(_cursorIndexOfLastStockUpdate)) {
              _tmpLastStockUpdate = null;
            } else {
              _tmpLastStockUpdate = _cursor.getLong(_cursorIndexOfLastStockUpdate);
            }
            _item = new Product(_tmpId,_tmpProductCode,_tmpName,_tmpDescription,_tmpCategoryId,_tmpSubCategoryId,_tmpPurchasePrice,_tmpSalePrice,_tmpCreditPrice,_tmpTotalStock,_tmpAvailableStock,_tmpReservedStock,_tmpMinStockLevel,_tmpColors,_tmpSizes,_tmpVariants,_tmpImageUrls,_tmpThumbnailUrl,_tmpIsActive,_tmpIsVisible,_tmpIsFeatured,_tmpCreatedBy,_tmpCreatedAt,_tmpUpdatedAt,_tmpLastStockUpdate);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Product>> searchProducts(final String query) {
    final String _sql = "SELECT * FROM products WHERE name LIKE '%' || ? || '%' OR description LIKE '%' || ? || '%'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    _argIndex = 2;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"products"}, new Callable<List<Product>>() {
      @Override
      @NonNull
      public List<Product> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfProductCode = CursorUtil.getColumnIndexOrThrow(_cursor, "productCode");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfSubCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subCategoryId");
          final int _cursorIndexOfPurchasePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "purchasePrice");
          final int _cursorIndexOfSalePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "salePrice");
          final int _cursorIndexOfCreditPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "creditPrice");
          final int _cursorIndexOfTotalStock = CursorUtil.getColumnIndexOrThrow(_cursor, "totalStock");
          final int _cursorIndexOfAvailableStock = CursorUtil.getColumnIndexOrThrow(_cursor, "availableStock");
          final int _cursorIndexOfReservedStock = CursorUtil.getColumnIndexOrThrow(_cursor, "reservedStock");
          final int _cursorIndexOfMinStockLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "minStockLevel");
          final int _cursorIndexOfColors = CursorUtil.getColumnIndexOrThrow(_cursor, "colors");
          final int _cursorIndexOfSizes = CursorUtil.getColumnIndexOrThrow(_cursor, "sizes");
          final int _cursorIndexOfVariants = CursorUtil.getColumnIndexOrThrow(_cursor, "variants");
          final int _cursorIndexOfImageUrls = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUrls");
          final int _cursorIndexOfThumbnailUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailUrl");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfIsVisible = CursorUtil.getColumnIndexOrThrow(_cursor, "isVisible");
          final int _cursorIndexOfIsFeatured = CursorUtil.getColumnIndexOrThrow(_cursor, "isFeatured");
          final int _cursorIndexOfCreatedBy = CursorUtil.getColumnIndexOrThrow(_cursor, "createdBy");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfLastStockUpdate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStockUpdate");
          final List<Product> _result = new ArrayList<Product>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Product _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpProductCode;
            if (_cursor.isNull(_cursorIndexOfProductCode)) {
              _tmpProductCode = null;
            } else {
              _tmpProductCode = _cursor.getString(_cursorIndexOfProductCode);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpCategoryId;
            if (_cursor.isNull(_cursorIndexOfCategoryId)) {
              _tmpCategoryId = null;
            } else {
              _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            }
            final String _tmpSubCategoryId;
            if (_cursor.isNull(_cursorIndexOfSubCategoryId)) {
              _tmpSubCategoryId = null;
            } else {
              _tmpSubCategoryId = _cursor.getString(_cursorIndexOfSubCategoryId);
            }
            final BigDecimal _tmpPurchasePrice;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfPurchasePrice)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfPurchasePrice);
            }
            _tmpPurchasePrice = __bigDecimalConverter.toBigDecimal(_tmp);
            final BigDecimal _tmpSalePrice;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfSalePrice)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfSalePrice);
            }
            _tmpSalePrice = __bigDecimalConverter.toBigDecimal(_tmp_1);
            final BigDecimal _tmpCreditPrice;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreditPrice)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfCreditPrice);
            }
            _tmpCreditPrice = __bigDecimalConverter.toBigDecimal(_tmp_2);
            final int _tmpTotalStock;
            _tmpTotalStock = _cursor.getInt(_cursorIndexOfTotalStock);
            final int _tmpAvailableStock;
            _tmpAvailableStock = _cursor.getInt(_cursorIndexOfAvailableStock);
            final int _tmpReservedStock;
            _tmpReservedStock = _cursor.getInt(_cursorIndexOfReservedStock);
            final int _tmpMinStockLevel;
            _tmpMinStockLevel = _cursor.getInt(_cursorIndexOfMinStockLevel);
            final List<String> _tmpColors;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfColors)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfColors);
            }
            _tmpColors = __typeConverters.toStringList(_tmp_3);
            final List<String> _tmpSizes;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfSizes)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfSizes);
            }
            _tmpSizes = __typeConverters.toStringList(_tmp_4);
            final List<ProductVariant> _tmpVariants;
            final String _tmp_5;
            if (_cursor.isNull(_cursorIndexOfVariants)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getString(_cursorIndexOfVariants);
            }
            _tmpVariants = __typeConverters.toProductVariantList(_tmp_5);
            final List<String> _tmpImageUrls;
            final String _tmp_6;
            if (_cursor.isNull(_cursorIndexOfImageUrls)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getString(_cursorIndexOfImageUrls);
            }
            _tmpImageUrls = __typeConverters.toStringList(_tmp_6);
            final String _tmpThumbnailUrl;
            if (_cursor.isNull(_cursorIndexOfThumbnailUrl)) {
              _tmpThumbnailUrl = null;
            } else {
              _tmpThumbnailUrl = _cursor.getString(_cursorIndexOfThumbnailUrl);
            }
            final boolean _tmpIsActive;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_7 != 0;
            final boolean _tmpIsVisible;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfIsVisible);
            _tmpIsVisible = _tmp_8 != 0;
            final boolean _tmpIsFeatured;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfIsFeatured);
            _tmpIsFeatured = _tmp_9 != 0;
            final String _tmpCreatedBy;
            if (_cursor.isNull(_cursorIndexOfCreatedBy)) {
              _tmpCreatedBy = null;
            } else {
              _tmpCreatedBy = _cursor.getString(_cursorIndexOfCreatedBy);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpUpdatedAt;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmpUpdatedAt = null;
            } else {
              _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Long _tmpLastStockUpdate;
            if (_cursor.isNull(_cursorIndexOfLastStockUpdate)) {
              _tmpLastStockUpdate = null;
            } else {
              _tmpLastStockUpdate = _cursor.getLong(_cursorIndexOfLastStockUpdate);
            }
            _item = new Product(_tmpId,_tmpProductCode,_tmpName,_tmpDescription,_tmpCategoryId,_tmpSubCategoryId,_tmpPurchasePrice,_tmpSalePrice,_tmpCreditPrice,_tmpTotalStock,_tmpAvailableStock,_tmpReservedStock,_tmpMinStockLevel,_tmpColors,_tmpSizes,_tmpVariants,_tmpImageUrls,_tmpThumbnailUrl,_tmpIsActive,_tmpIsVisible,_tmpIsFeatured,_tmpCreatedBy,_tmpCreatedAt,_tmpUpdatedAt,_tmpLastStockUpdate);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}

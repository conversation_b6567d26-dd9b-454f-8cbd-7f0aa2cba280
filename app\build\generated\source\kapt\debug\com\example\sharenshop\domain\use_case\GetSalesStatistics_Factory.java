// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.StatisticsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetSalesStatistics_Factory implements Factory<GetSalesStatistics> {
  private final Provider<StatisticsRepository> repositoryProvider;

  public GetSalesStatistics_Factory(Provider<StatisticsRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public GetSalesStatistics get() {
    return newInstance(repositoryProvider.get());
  }

  public static GetSalesStatistics_Factory create(
      Provider<StatisticsRepository> repositoryProvider) {
    return new GetSalesStatistics_Factory(repositoryProvider);
  }

  public static GetSalesStatistics newInstance(StatisticsRepository repository) {
    return new GetSalesStatistics(repository);
  }
}

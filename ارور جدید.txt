2025-06-17 06:22:57.666  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137287825, oldVsyncId=137287464, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:22:58.151  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137288186, oldVsyncId=137287825, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:22:58.653  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137288529, oldVsyncId=137288186, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:22:59.153  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137288974, oldVsyncId=137288529, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:22:59.655  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137289335, oldVsyncId=137288974, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:00.156  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137289956, oldVsyncId=137289943, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:00.657  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137289987, oldVsyncId=137289956, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.158  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290168, oldVsyncId=137289987, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.511  2471-2682  MIUIInput               system_server                        I  [MotionEvent] publisher action=0x0, deviceId=5, 361448989, channel '12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity (server)'
2025-06-17 06:23:01.520 28096-28096 MIUIInput               com.example.sharenshop               I  [MotionEvent] ViewRootImpl windowName 'com.example.sharenshop/com.example.sharenshop.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=361448989, downTime=361448989, phoneEventTime=۰۶:۲۳:۰۱.۵۰۹ } moveCount:0
2025-06-17 06:23:01.559  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290373, oldVsyncId=137290168, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.576  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290662, oldVsyncId=137290373, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.592  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290681, oldVsyncId=137290662, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.609  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290700, oldVsyncId=137290681, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.626  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290719, oldVsyncId=137290700, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.642  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290738, oldVsyncId=137290719, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.659  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290757, oldVsyncId=137290738, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.666  2471-2682  MIUIInput               system_server                        I  [MotionEvent] publisher action=0x1, deviceId=5, 361449142, channel '12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity (server)'
2025-06-17 06:23:01.667 28096-28096 MIUIInput               com.example.sharenshop               I  [MotionEvent] ViewRootImpl windowName 'com.example.sharenshop/com.example.sharenshop.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=361449142, downTime=361448989, phoneEventTime=۰۶:۲۳:۰۱.۶۶۲ } moveCount:0
2025-06-17 06:23:01.669 28096-28096 AuthViewModel           com.example.sharenshop               D  Starting authentication...
2025-06-17 06:23:01.669 28096-28096 AuthViewModel           com.example.sharenshop               D  Email: <EMAIL>
2025-06-17 06:23:01.669 28096-28096 AuthViewModel           com.example.sharenshop               D  IsLoginMode: true
2025-06-17 06:23:01.669 28096-28096 AuthViewModel           com.example.sharenshop               D  IsCustomerSignupMode: false
2025-06-17 06:23:01.669 28096-28096 AuthViewModel           com.example.sharenshop               D  🔄 شروع احراز هویت...
2025-06-17 06:23:01.669 28096-28096 AuthViewModel           com.example.sharenshop               D  📧 Email: <EMAIL>
2025-06-17 06:23:01.669 28096-28096 AuthViewModel           com.example.sharenshop               D  🔐 Password length: 11
2025-06-17 06:23:01.670 28096-28096 AuthViewModel           com.example.sharenshop               D  🔄 تست اتصال به Supabase...
2025-06-17 06:23:01.670 28096-28096 SupabaseClient          com.example.sharenshop               D  🔄 Testing Supabase connection...
2025-06-17 06:23:01.670 28096-28096 SupabaseClient          com.example.sharenshop               D  📱 Current session: <EMAIL>
2025-06-17 06:23:01.670 28096-28096 SupabaseClient          com.example.sharenshop               D  ✅ Database connection test successful
2025-06-17 06:23:01.670 28096-28096 AuthViewModel           com.example.sharenshop               D  🔄 ارسال درخواست ورود به Supabase...
2025-06-17 06:23:01.670 28096-28096 AuthViewModel           com.example.sharenshop               D  📧 Attempting login with email: <EMAIL>
2025-06-17 06:23:01.676  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290776, oldVsyncId=137290757, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.692  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290795, oldVsyncId=137290776, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.759  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290814, oldVsyncId=137290795, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.776  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290863, oldVsyncId=137290814, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.776  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290863, oldVsyncId=137290863, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.792  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290882, oldVsyncId=137290863, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.793  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290882, oldVsyncId=137290882, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.809  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290901, oldVsyncId=137290882, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.809  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290901, oldVsyncId=137290901, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.826  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290920, oldVsyncId=137290901, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.826  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290920, oldVsyncId=137290920, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.843  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290939, oldVsyncId=137290920, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.843  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290939, oldVsyncId=137290939, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.859  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290958, oldVsyncId=137290939, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.859  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290958, oldVsyncId=137290958, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.876  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290977, oldVsyncId=137290958, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.876  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290977, oldVsyncId=137290977, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.893  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290996, oldVsyncId=137290977, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.893  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137290996, oldVsyncId=137290996, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.909  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291015, oldVsyncId=137290996, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.910  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291015, oldVsyncId=137291015, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.926  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291034, oldVsyncId=137291015, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.926  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291034, oldVsyncId=137291034, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.943  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291053, oldVsyncId=137291034, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.943  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291053, oldVsyncId=137291053, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.960  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291072, oldVsyncId=137291053, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.976  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291091, oldVsyncId=137291072, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.977  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291091, oldVsyncId=137291091, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.993  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291110, oldVsyncId=137291091, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:01.993  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291110, oldVsyncId=137291110, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.010  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291129, oldVsyncId=137291110, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.010  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291129, oldVsyncId=137291129, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.026  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291148, oldVsyncId=137291129, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.027  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291148, oldVsyncId=137291148, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.043  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291167, oldVsyncId=137291148, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.043  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291167, oldVsyncId=137291167, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.060  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291186, oldVsyncId=137291167, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.060  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291186, oldVsyncId=137291186, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.077  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291205, oldVsyncId=137291186, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.077  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291205, oldVsyncId=137291205, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.093  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291224, oldVsyncId=137291205, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.094  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291224, oldVsyncId=137291224, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.110  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291243, oldVsyncId=137291224, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.111  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291243, oldVsyncId=137291243, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.127  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291262, oldVsyncId=137291243, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.127  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291262, oldVsyncId=137291262, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.143  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291281, oldVsyncId=137291262, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.144  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291281, oldVsyncId=137291281, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.160  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291300, oldVsyncId=137291281, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.160  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291300, oldVsyncId=137291300, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.177  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291319, oldVsyncId=137291300, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.177  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291319, oldVsyncId=137291319, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.193  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291338, oldVsyncId=137291319, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.194  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291338, oldVsyncId=137291338, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.211  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291357, oldVsyncId=137291338, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.211  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291357, oldVsyncId=137291357, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.227  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291376, oldVsyncId=137291357, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.227  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291376, oldVsyncId=137291376, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.244  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291395, oldVsyncId=137291376, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.244  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291395, oldVsyncId=137291395, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.260  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291414, oldVsyncId=137291395, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.260  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291414, oldVsyncId=137291414, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.277  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291433, oldVsyncId=137291414, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.277  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291433, oldVsyncId=137291433, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.294  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291452, oldVsyncId=137291433, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.294  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291452, oldVsyncId=137291452, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.310  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291471, oldVsyncId=137291452, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.310  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291471, oldVsyncId=137291471, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.328  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291490, oldVsyncId=137291471, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.328  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291490, oldVsyncId=137291490, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.344  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291509, oldVsyncId=137291490, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.344  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291509, oldVsyncId=137291509, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.360  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291528, oldVsyncId=137291509, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.361  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291528, oldVsyncId=137291528, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.377  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291547, oldVsyncId=137291528, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.378  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291547, oldVsyncId=137291547, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.394  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291566, oldVsyncId=137291547, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.394  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291566, oldVsyncId=137291566, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.411  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291585, oldVsyncId=137291566, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.411  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291585, oldVsyncId=137291585, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.427  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291604, oldVsyncId=137291585, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.427  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291604, oldVsyncId=137291604, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.444  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291617, oldVsyncId=137291604, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.444  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291617, oldVsyncId=137291617, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.461  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291630, oldVsyncId=137291617, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.461  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291630, oldVsyncId=137291630, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.478  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291643, oldVsyncId=137291630, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.478  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291643, oldVsyncId=137291643, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.494  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291656, oldVsyncId=137291643, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.494  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291656, oldVsyncId=137291656, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.511  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291669, oldVsyncId=137291656, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.511  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291669, oldVsyncId=137291669, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.528  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291682, oldVsyncId=137291669, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.528  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291682, oldVsyncId=137291682, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.544  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291695, oldVsyncId=137291682, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.544  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291695, oldVsyncId=137291695, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.561  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291708, oldVsyncId=137291695, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.561  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291708, oldVsyncId=137291708, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.578  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291721, oldVsyncId=137291708, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.578  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291721, oldVsyncId=137291721, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.594  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291734, oldVsyncId=137291721, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.595  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291734, oldVsyncId=137291734, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.611  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291747, oldVsyncId=137291734, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.617 28096-28096 AuthViewModel           com.example.sharenshop               D  ✅ ورود به Supabase موفق بود
2025-06-17 06:23:02.617 28096-28096 AuthViewModel           com.example.sharenshop               D  🔑 Session created successfully
2025-06-17 06:23:02.617 28096-28096 AuthViewModel           com.example.sharenshop               D  🔍 Auth result: kotlin.Unit
2025-06-17 06:23:02.618 28096-28096 AuthViewModel           com.example.sharenshop               D  👤 User ID: 11111111-1111-1111-1111-111111111111
2025-06-17 06:23:02.618 28096-28096 AuthViewModel           com.example.sharenshop               D  📧 User Email: <EMAIL>
2025-06-17 06:23:02.618 28096-28096 AuthViewModel           com.example.sharenshop               D  ✅ Email Confirmed: true
2025-06-17 06:23:02.618 28096-28096 AuthViewModel           com.example.sharenshop               D  🔄 بررسی اطلاعات کاربر در پایگاه داده...
2025-06-17 06:23:02.618 28096-28096 UserRepositoryImpl      com.example.sharenshop               D  Getting user by email: <EMAIL>
2025-06-17 06:23:02.618 28096-28096 AuthViewModel           com.example.sharenshop               E  ❌ کاربر در پایگاه داده یافت نشد
2025-06-17 06:23:02.618 28096-28096 AuthViewModel           com.example.sharenshop               E  ❌ خطا در بررسی کاربر: کاربر در سیستم یافت نشد
2025-06-17 06:23:02.618 28096-28096 AuthViewModel           com.example.sharenshop               E  ❌ خطای احراز هویت: کاربر در سیستم یافت نشد
2025-06-17 06:23:02.618 28096-28096 AuthViewModel           com.example.sharenshop               E  ❌ Exception type: Exception
2025-06-17 06:23:02.619 28096-28096 AuthViewModel           com.example.sharenshop               E  ❌ Stack trace: 
                                                                                                    java.lang.Exception: کاربر در سیستم یافت نشد
                                                                                                    	at com.example.sharenshop.presentation.auth.AuthViewModel.authenticateUser(AuthViewModel.kt:233)
                                                                                                    	at com.example.sharenshop.presentation.auth.AuthViewModel.access$authenticateUser(AuthViewModel.kt:21)
                                                                                                    	at com.example.sharenshop.presentation.auth.AuthViewModel$authenticateUser$1.invokeSuspend(Unknown Source:14)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:959)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:100)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:249)
                                                                                                    	at android.os.Looper.loop(Looper.java:337)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9593)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:593)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-06-17 06:23:02.628  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291760, oldVsyncId=137291747, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.711  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291767, oldVsyncId=137291760, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.728  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291810, oldVsyncId=137291767, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:02.752  2471-2682  MIUIInput               system_server                        I  [MotionEvent] publisher action=0x0, deviceId=5, 361450228, channel '12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity (server)'
2025-06-17 06:23:02.754 28096-28096 MIUIInput               com.example.sharenshop               I  [MotionEvent] ViewRootImpl windowName 'com.example.sharenshop/com.example.sharenshop.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=361450228, downTime=361450228, phoneEventTime=۰۶:۲۳:۰۲.۷۴۸ } moveCount:0
2025-06-17 06:23:03.162  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137291817, oldVsyncId=137291810, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:03.169  2471-2682  MIUIInput               system_server                        I  [MotionEvent] publisher action=0x1, deviceId=5, 361450646, channel '12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity (server)'
2025-06-17 06:23:03.174 28096-28096 MIUIInput               com.example.sharenshop               I  [MotionEvent] ViewRootImpl windowName 'com.example.sharenshop/com.example.sharenshop.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=361450646, downTime=361450228, phoneEventTime=۰۶:۲۳:۰۳.۱۶۶ } moveCount:24
2025-06-17 06:23:03.454  2471-2682  MIUIInput               system_server                        I  [MotionEvent] publisher action=0x0, deviceId=5, 361450930, channel '12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity (server)'
2025-06-17 06:23:03.458 28096-28096 MIUIInput               com.example.sharenshop               I  [MotionEvent] ViewRootImpl windowName 'com.example.sharenshop/com.example.sharenshop.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=361450930, downTime=361450930, phoneEventTime=۰۶:۲۳:۰۳.۴۵۱ } moveCount:0
2025-06-17 06:23:03.542  2471-2682  MIUIInput               system_server                        I  [MotionEvent] publisher action=0x1, deviceId=5, 361451018, channel '12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity (server)'
2025-06-17 06:23:03.550 28096-28096 MIUIInput               com.example.sharenshop               I  [MotionEvent] ViewRootImpl windowName 'com.example.sharenshop/com.example.sharenshop.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=361451018, downTime=361450930, phoneEventTime=۰۶:۲۳:۰۳.۵۳۸ } moveCount:1
2025-06-17 06:23:03.629 28096-28096 HandWritingStubImpl     com.example.sharenshop               I  getCurrentKeyboardType: 1
2025-06-17 06:23:03.631 28096-28096 InsetsController        com.example.sharenshop               D  show(ime(), fromIme=false)
2025-06-17 06:23:03.632 28096-28096 ImeTracker              com.example.sharenshop               I  com.example.sharenshop:c54f770f: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT_BY_INSETS_API fromUser false
2025-06-17 06:23:03.633 28096-28096 InputMethodManager      com.example.sharenshop               D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{9819e8d VFED..... .F....ID 0,0-1080,2400 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
2025-06-17 06:23:03.644  1342-5185  MI-SF                   surfaceflinger                       D  updateScene: module = 270 (Input), value = 1, pkg = com.example.sharenshop, Vrr = 0, mSetTpIdleFlag = 0
2025-06-17 06:23:03.646  5073-5073  GoogleInpu...hodService com...gle.android.inputmethod.latin  I  GoogleInputMethodService.onStartInput():1247 onStartInput(EditorInfo{EditorInfo{packageName=com.example.sharenshop, inputType=28001, inputTypeString=Normal[MultiLine|AutoCorrect], enableLearning=true, autoCorrection=true, autoComplete=true, imeOptions=42000000, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=11, initialSelEnd=11, initialCapsMode=0, label=null, fieldId=-1, fieldName=null, extras=Bundle[mParcelledData.dataSize=308], hintText=null, hintLocales=[]}}, true)
2025-06-17 06:23:03.649  2471-6097  PackageConfigPersister  system_server                        W  App-specific configuration not found for packageName: com.example.sharenshop and userId: 0
2025-06-17 06:23:03.651  5073-5073  GoogleInpu...hodService com...gle.android.inputmethod.latin  I  GoogleInputMethodService.onStartInputView():1342 onStartInputView(EditorInfo{EditorInfo{packageName=com.example.sharenshop, inputType=28001, inputTypeString=Normal[MultiLine|AutoCorrect], enableLearning=true, autoCorrection=true, autoComplete=true, imeOptions=42000000, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=11, initialSelEnd=11, initialCapsMode=0, label=null, fieldId=-1, fieldName=null, extras=Bundle[{android.support.text.emoji.emojiCompat_metadataVersion=11, android.support.text.emoji.emojiCompat_replaceAll=false, hand_writing_keyboard_type=1}], hintText=null, hintLocales=[]}}, false)
2025-06-17 06:23:03.664  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292130, oldVsyncId=137291817, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:03.731  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292499, oldVsyncId=137292461, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:03.731  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292499, oldVsyncId=137292499, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:03.749  2471-3555  WindowManager           system_server                        D  wms.Input focus has changed to Window{12f1b3d u0 com.example.sharenshop/com.example.sharenshop.MainActivity} display=0 updateInputWindows = true
2025-06-17 06:23:03.756 28096-28096 libc                    com.example.sharenshop               W  Access denied finding property "vendor.display.enable_optimal_refresh_rate"
2025-06-17 06:23:03.756 28096-28096 libc                    com.example.sharenshop               W  Access denied finding property "vendor.gpp.create_frc_extension"
2025-06-17 06:23:03.749 28096-28096 mple.sharenshop         com.example.sharenshop               W  type=1400 audit(0.0:329389): avc:  denied  { read } for  name="u:object_r:vendor_display_prop:s0" dev="tmpfs" ino=457 scontext=u:r:untrusted_app:s0:c131,c257,c512,c768 tcontext=u:object_r:vendor_display_prop:s0 tclass=file permissive=0 app=com.example.sharenshop
2025-06-17 06:23:03.763 28096-28096 VRI[پنجره بالاپر]       com.example.sharenshop               D  vri.reportNextDraw android.view.ViewRootImpl.performTraversals:4945 android.view.ViewRootImpl.doTraversal:3556 android.view.ViewRootImpl$TraversalRunnable.run:11567 android.view.Choreographer$CallbackRecord.run:1747 android.view.Choreographer$CallbackRecord.run:1756 
2025-06-17 06:23:03.763 28096-28096 VRI[پنجره بالاپر]       com.example.sharenshop               D  vri.Setup new sync=wmsSync-VRI[پنجره بالاپر]#20
2025-06-17 06:23:03.767 28096-28268 HWUI                    com.example.sharenshop               D  makeCurrent grContext:0xb400007a906d0e50 reset mTextureAvailable
2025-06-17 06:23:03.772 28096-28096 VRI[پنجره بالاپر]       com.example.sharenshop               D  vri.reportDrawFinished
2025-06-17 06:23:03.777  5073-5073  AndroidIME              com...gle.android.inputmethod.latin  I  AbstractIme.onActivate():95 LatinIme.onActivate() : EditorInfo = EditorInfo{packageName=com.example.sharenshop, inputType=28001, inputTypeString=Normal[MultiLine|AutoCorrect], enableLearning=true, autoCorrection=true, autoComplete=true, imeOptions=42000000, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=11, initialSelEnd=11, initialCapsMode=0, label=null, fieldId=-1, fieldName=null, extras=Bundle[{android.support.text.emoji.emojiCompat_metadataVersion=11, android.support.text.emoji.emojiCompat_replaceAll=false, hand_writing_keyboard_type=1}], hintText=null, hintLocales=[]}, IncognitoMode = false, DeviceLocked = false
2025-06-17 06:23:03.781  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292532, oldVsyncId=137292532, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:03.788  2471-6097  InputDispatcher         system_server                        D  setFocusedWindow 12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity on display 0 ignored, reason: already focused
2025-06-17 06:23:03.797  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292545, oldVsyncId=137292532, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:03.824 28096-28096 VRI[پنجره بالاپر]       com.example.sharenshop               D  vri.reportNextDraw android.view.ViewRootImpl.handleResized:2769 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:12725 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-06-17 06:23:03.831  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292565, oldVsyncId=137292565, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:03.837  2471-3555  WindowManager           system_server                        I  WMS.showImePostLayout(), imeTarget = Window{12f1b3d u0 com.example.sharenshop/com.example.sharenshop.MainActivity}
2025-06-17 06:23:03.848  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292590, oldVsyncId=137292590, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:03.861 28096-28096 VRI[پنجره بالاپر]       com.example.sharenshop               D  vri.Setup new sync=wmsSync-VRI[پنجره بالاپر]#22
2025-06-17 06:23:03.862 28096-28096 VRI[پنجره بالاپر]       com.example.sharenshop               D  vri.reportDrawFinished
2025-06-17 06:23:03.864  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292610, oldVsyncId=137292590, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:03.881  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292629, oldVsyncId=137292610, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:03.915  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292655, oldVsyncId=137292655, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:03.915  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292655, oldVsyncId=137292655, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:03.931  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292668, oldVsyncId=137292668, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:03.934 28096-28096 InsetsController        com.example.sharenshop               D  show(ime(), fromIme=true)
2025-06-17 06:23:03.938 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  requestedTypes: 8
2025-06-17 06:23:03.948  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292681, oldVsyncId=137292681, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:03.948 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationStart
2025-06-17 06:23:03.959 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.0
2025-06-17 06:23:03.964  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292706, oldVsyncId=137292706, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:03.966 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.04765857
2025-06-17 06:23:03.970  2471-3555  PerfShielderService     system_server                        D  com.example.sharenshop|com.example.sharenshop/com.example.sharenshop.MainActivity|356|361451382953729|116|2|5
2025-06-17 06:23:03.971  5242-8215  MiuiPerfServiceClient   com.miui.daemon                      W  interceptAndQueuing:28096|com.example.sharenshop|356|116|361451382953729|Slow handle animation|5
2025-06-17 06:23:03.981  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292737, oldVsyncId=137292737, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:03.982 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.16040812
2025-06-17 06:23:03.998  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292768, oldVsyncId=137292737, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:04.000 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.2924216
2025-06-17 06:23:04.014  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292793, oldVsyncId=137292768, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:04.015  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292793, oldVsyncId=137292793, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:04.015 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.42073354
2025-06-17 06:23:04.031  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292824, oldVsyncId=137292824, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:04.031  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292824, oldVsyncId=137292824, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:04.032 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.5351085
2025-06-17 06:23:04.048  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292855, oldVsyncId=137292824, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:04.048  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292855, oldVsyncId=137292855, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:04.049 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.6321573
2025-06-17 06:23:04.065  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292880, oldVsyncId=137292880, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:04.065  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292880, oldVsyncId=137292880, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:04.066 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.71196556
2025-06-17 06:23:04.081  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292905, oldVsyncId=137292905, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:04.081  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292905, oldVsyncId=137292905, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:04.084 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.77621686
2025-06-17 06:23:04.098  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292930, oldVsyncId=137292905, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:04.098  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292930, oldVsyncId=137292930, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:04.101 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.8271712
2025-06-17 06:23:04.115  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292955, oldVsyncId=137292930, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:04.115  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292955, oldVsyncId=137292955, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:04.118 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.86713785
2025-06-17 06:23:04.131  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292980, oldVsyncId=137292955, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:04.132  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137292980, oldVsyncId=137292980, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:04.135 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.8982288
2025-06-17 06:23:04.148  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137293005, oldVsyncId=137292980, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:04.149  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137293005, oldVsyncId=137293005, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:04.152 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.92226404
2025-06-17 06:23:04.165  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137293030, oldVsyncId=137293005, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:04.165  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137293030, oldVsyncId=137293030, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:04.168 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.94075537
2025-06-17 06:23:04.182  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137293055, oldVsyncId=137293055, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:04.184 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.954928
2025-06-17 06:23:04.198  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137293080, oldVsyncId=137293055, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:04.199  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137293080, oldVsyncId=137293080, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:04.199 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9657585
2025-06-17 06:23:04.215  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137293105, oldVsyncId=137293105, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:04.218 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9740159
2025-06-17 06:23:04.232  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137293130, oldVsyncId=137293130, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:04.234 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9802998
2025-06-17 06:23:04.248  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137293155, oldVsyncId=137293155, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:04.251 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9850748
2025-06-17 06:23:04.266 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.988699
2025-06-17 06:23:04.285 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9914472
2025-06-17 06:23:04.300 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9935295
2025-06-17 06:23:04.318 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.99510634
2025-06-17 06:23:04.335 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9962998
2025-06-17 06:23:04.351 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.99720275
2025-06-17 06:23:04.368 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9978857
2025-06-17 06:23:04.385 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9984021
2025-06-17 06:23:04.400 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9987925
2025-06-17 06:23:04.417 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 1.0
2025-06-17 06:23:04.418 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationEnd,canceled: false
2025-06-17 06:23:04.420 28096-28096 ImeTracker              com.example.sharenshop               I  com.example.sharenshop:c54f770f: onShown
2025-06-17 06:23:04.700  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137293395, oldVsyncId=137293376, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:05.218  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137293588, oldVsyncId=137293395, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:05.305  2471-2682  MIUIInput               system_server                        I  [KeyEvent] publisher action:0x0,  keyCode:4 , deviceId=-1, 361452684,channel '12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity (server)' 
2025-06-17 06:23:05.309 28096-28096 MIUIInput               com.example.sharenshop               I  [KeyEvent] ViewRootImpl windowName 'com.example.sharenshop/com.example.sharenshop.MainActivity', KeyEvent { action=ACTION_DOWN, keyCode=KEYCODE_BACK, scanCode=0, metaState=0, flags=0x48, repeatCount=0, eventTime=361452684000000, downTime=361452684000000, deviceId=-1, source=0x101, displayId=0 }, phoneEventTime=۰۶:۲۳:۰۵.۲۰۴
2025-06-17 06:23:05.316  2471-2682  MIUIInput               system_server                        I  [KeyEvent] publisher action:0x1,  keyCode:4 , deviceId=-1, 361452776,channel '12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity (server)' 
2025-06-17 06:23:05.323 28096-28096 MIUIInput               com.example.sharenshop               I  [KeyEvent] ViewRootImpl windowName 'com.example.sharenshop/com.example.sharenshop.MainActivity', KeyEvent { action=ACTION_UP, keyCode=KEYCODE_BACK, scanCode=0, metaState=0, flags=0x48, repeatCount=0, eventTime=361452776000000, downTime=361452684000000, deviceId=-1, source=0x101, displayId=0 }, phoneEventTime=۰۶:۲۳:۰۵.۲۹۷
2025-06-17 06:23:05.339  1342-1526  MI-SF                   surfaceflinger                       D  updateScene: module = 270 (Input), value = 0, pkg = com.example.sharenshop, Vrr = 0, mSetTpIdleFlag = 0
2025-06-17 06:23:05.342 28096-28096 InsetsController        com.example.sharenshop               D  hide(ime(), fromIme=true)
2025-06-17 06:23:05.344 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  requestedTypes: 8
2025-06-17 06:23:05.355 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationStart
2025-06-17 06:23:05.366 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.0
2025-06-17 06:23:05.385  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294107, oldVsyncId=137294107, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:05.385  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294107, oldVsyncId=137294107, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:05.385 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.053801965
2025-06-17 06:23:05.401  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294126, oldVsyncId=137294126, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:05.401  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294126, oldVsyncId=137294126, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:05.402 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.1784273
2025-06-17 06:23:05.418  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294157, oldVsyncId=137294157, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:05.418 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.31967008
2025-06-17 06:23:05.435  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294188, oldVsyncId=137294188, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:05.435  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294188, oldVsyncId=137294188, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:05.437 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.45275992
2025-06-17 06:23:05.451  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294219, oldVsyncId=137294188, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:05.451  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294219, oldVsyncId=137294219, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:05.454 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.56807214
2025-06-17 06:23:05.468  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294250, oldVsyncId=137294219, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:05.468  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294250, oldVsyncId=137294250, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:05.471 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.663439
2025-06-17 06:23:05.485  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294281, oldVsyncId=137294250, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:05.485  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294281, oldVsyncId=137294281, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:05.488 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.74008745
2025-06-17 06:23:05.501  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294312, oldVsyncId=137294281, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:05.501  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294312, oldVsyncId=137294312, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:05.503 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.800552
2025-06-17 06:23:05.518  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294343, oldVsyncId=137294312, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:05.518  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294343, oldVsyncId=137294343, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:05.519 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.8476492
2025-06-17 06:23:05.535  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294374, oldVsyncId=137294374, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:05.535  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294374, oldVsyncId=137294374, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:05.536 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.8840107
2025-06-17 06:23:05.551  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294405, oldVsyncId=137294374, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:05.553 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9119078
2025-06-17 06:23:05.568  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294436, oldVsyncId=137294405, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:05.568  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294436, oldVsyncId=137294436, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:05.570 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.93321526
2025-06-17 06:23:05.585  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294467, oldVsyncId=137294467, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:05.587 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.94943553
2025-06-17 06:23:05.602  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294498, oldVsyncId=137294467, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:05.602  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294498, oldVsyncId=137294498, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:05.602 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.96175355
2025-06-17 06:23:05.618  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294529, oldVsyncId=137294529, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:05.621 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9710916
2025-06-17 06:23:05.635  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294560, oldVsyncId=137294560, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:05.639 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9781616
2025-06-17 06:23:05.652  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294591, oldVsyncId=137294591, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:05.655 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9835091
2025-06-17 06:23:05.672 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.98755103
2025-06-17 06:23:05.686 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.99060434
2025-06-17 06:23:05.702  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294678, oldVsyncId=137294678, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:05.705 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.99290997
2025-06-17 06:23:05.722 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9946504
2025-06-17 06:23:05.738 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.995964
2025-06-17 06:23:05.755 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9969552
2025-06-17 06:23:05.771 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.99770314
2025-06-17 06:23:05.789 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9982674
2025-06-17 06:23:05.805 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 0.9986931
2025-06-17 06:23:05.822 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationUpdate, value: 1.0
2025-06-17 06:23:05.823 28096-28309 ViewRootImplStubImpl    com.example.sharenshop               D  onAnimationEnd,canceled: false
2025-06-17 06:23:05.829 28096-28096 ImeTracker              com.example.sharenshop               I  com.example.sharenshop:9c68899c: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_ON_ANIMATION_STATE_CHANGED fromUser false
2025-06-17 06:23:05.831  2471-2692  ImeTracker              system_server                        I  com.example.sharenshop:9c68899c: onCancelled at PHASE_SERVER_SHOULD_HIDE
2025-06-17 06:23:05.831 28096-28096 ImeTracker              com.example.sharenshop               I  com.google.android.inputmethod.latin:8307c029: onHidden
2025-06-17 06:23:06.064  2471-2682  MIUIInput               system_server                        I  [MotionEvent] publisher action=0x0, deviceId=5, 361453541, channel '12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity (server)'
2025-06-17 06:23:06.067 28096-28096 MIUIInput               com.example.sharenshop               I  [MotionEvent] ViewRootImpl windowName 'com.example.sharenshop/com.example.sharenshop.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=361453541, downTime=361453541, phoneEventTime=۰۶:۲۳:۰۶.۰۶۲ } moveCount:0
2025-06-17 06:23:06.103  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294885, oldVsyncId=137294866, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.103  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137294885, oldVsyncId=137294885, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.119  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295078, oldVsyncId=137294885, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.136  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295097, oldVsyncId=137295078, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.143  2471-2682  MIUIInput               system_server                        I  [MotionEvent] publisher action=0x1, deviceId=5, 361453620, channel '12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity (server)'
2025-06-17 06:23:06.146 28096-28096 MIUIInput               com.example.sharenshop               I  [MotionEvent] ViewRootImpl windowName 'com.example.sharenshop/com.example.sharenshop.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=361453620, downTime=361453541, phoneEventTime=۰۶:۲۳:۰۶.۱۴۱ } moveCount:0
2025-06-17 06:23:06.149 28096-28096 AuthViewModel           com.example.sharenshop               D  Starting authentication...
2025-06-17 06:23:06.149 28096-28096 AuthViewModel           com.example.sharenshop               D  Email: <EMAIL>
2025-06-17 06:23:06.149 28096-28096 AuthViewModel           com.example.sharenshop               D  IsLoginMode: true
2025-06-17 06:23:06.149 28096-28096 AuthViewModel           com.example.sharenshop               D  IsCustomerSignupMode: false
2025-06-17 06:23:06.149 28096-28096 AuthViewModel           com.example.sharenshop               D  🔄 شروع احراز هویت...
2025-06-17 06:23:06.149 28096-28096 AuthViewModel           com.example.sharenshop               D  📧 Email: <EMAIL>
2025-06-17 06:23:06.149 28096-28096 AuthViewModel           com.example.sharenshop               D  🔐 Password length: 11
2025-06-17 06:23:06.149 28096-28096 AuthViewModel           com.example.sharenshop               D  🔄 تست اتصال به Supabase...
2025-06-17 06:23:06.149 28096-28096 SupabaseClient          com.example.sharenshop               D  🔄 Testing Supabase connection...
2025-06-17 06:23:06.149 28096-28096 SupabaseClient          com.example.sharenshop               D  📱 Current session: <EMAIL>
2025-06-17 06:23:06.150 28096-28096 SupabaseClient          com.example.sharenshop               D  ✅ Database connection test successful
2025-06-17 06:23:06.150 28096-28096 AuthViewModel           com.example.sharenshop               D  🔄 ارسال درخواست ورود به Supabase...
2025-06-17 06:23:06.150 28096-28096 AuthViewModel           com.example.sharenshop               D  📧 Attempting login with email: <EMAIL>
2025-06-17 06:23:06.236  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295116, oldVsyncId=137295097, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.253  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295189, oldVsyncId=137295116, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.270  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295202, oldVsyncId=137295189, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.270  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295202, oldVsyncId=137295202, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:06.287  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295227, oldVsyncId=137295202, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.287  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295227, oldVsyncId=137295227, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:06.303  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295252, oldVsyncId=137295227, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.320  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295277, oldVsyncId=137295252, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.337  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295296, oldVsyncId=137295277, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.354  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295315, oldVsyncId=137295296, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.370  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295340, oldVsyncId=137295315, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.387  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295359, oldVsyncId=137295340, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.404  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295372, oldVsyncId=137295359, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.420  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295391, oldVsyncId=137295372, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.420  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295391, oldVsyncId=137295391, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:06.437  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295416, oldVsyncId=137295391, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.454  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295441, oldVsyncId=137295416, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.470  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295460, oldVsyncId=137295441, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.487  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295479, oldVsyncId=137295460, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.504  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295498, oldVsyncId=137295479, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.520  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295517, oldVsyncId=137295498, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.537  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295536, oldVsyncId=137295517, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.554  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295555, oldVsyncId=137295536, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.571  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295574, oldVsyncId=137295555, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.587  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295593, oldVsyncId=137295574, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.621  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295612, oldVsyncId=137295593, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.621  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295612, oldVsyncId=137295612, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.638  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295649, oldVsyncId=137295612, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.638  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295649, oldVsyncId=137295649, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.643 28096-28096 AuthViewModel           com.example.sharenshop               D  ✅ ورود به Supabase موفق بود
2025-06-17 06:23:06.644 28096-28096 AuthViewModel           com.example.sharenshop               D  🔑 Session created successfully
2025-06-17 06:23:06.644 28096-28096 AuthViewModel           com.example.sharenshop               D  🔍 Auth result: kotlin.Unit
2025-06-17 06:23:06.644 28096-28096 AuthViewModel           com.example.sharenshop               D  👤 User ID: 11111111-1111-1111-1111-111111111111
2025-06-17 06:23:06.644 28096-28096 AuthViewModel           com.example.sharenshop               D  📧 User Email: <EMAIL>
2025-06-17 06:23:06.644 28096-28096 AuthViewModel           com.example.sharenshop               D  ✅ Email Confirmed: true
2025-06-17 06:23:06.644 28096-28096 AuthViewModel           com.example.sharenshop               D  🔄 بررسی اطلاعات کاربر در پایگاه داده...
2025-06-17 06:23:06.644 28096-28096 UserRepositoryImpl      com.example.sharenshop               D  Getting user by email: <EMAIL>
2025-06-17 06:23:06.644 28096-28096 AuthViewModel           com.example.sharenshop               E  ❌ کاربر در پایگاه داده یافت نشد
2025-06-17 06:23:06.644 28096-28096 AuthViewModel           com.example.sharenshop               E  ❌ خطا در بررسی کاربر: کاربر در سیستم یافت نشد
2025-06-17 06:23:06.644 28096-28096 AuthViewModel           com.example.sharenshop               E  ❌ خطای احراز هویت: کاربر در سیستم یافت نشد
2025-06-17 06:23:06.644 28096-28096 AuthViewModel           com.example.sharenshop               E  ❌ Exception type: Exception
2025-06-17 06:23:06.645 28096-28096 AuthViewModel           com.example.sharenshop               E  ❌ Stack trace: 
                                                                                                    java.lang.Exception: کاربر در سیستم یافت نشد
                                                                                                    	at com.example.sharenshop.presentation.auth.AuthViewModel.authenticateUser(AuthViewModel.kt:233)
                                                                                                    	at com.example.sharenshop.presentation.auth.AuthViewModel.access$authenticateUser(AuthViewModel.kt:21)
                                                                                                    	at com.example.sharenshop.presentation.auth.AuthViewModel$authenticateUser$1.invokeSuspend(Unknown Source:14)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:959)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:100)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:249)
                                                                                                    	at android.os.Looper.loop(Looper.java:337)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9593)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:593)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-06-17 06:23:06.654  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295662, oldVsyncId=137295649, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.654  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295662, oldVsyncId=137295662, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.671  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295675, oldVsyncId=137295662, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.754  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295694, oldVsyncId=137295675, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.771  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295743, oldVsyncId=137295694, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.788  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295763, oldVsyncId=137295743, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.788  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295763, oldVsyncId=137295763, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:06.805  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295788, oldVsyncId=137295763, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.805  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295788, oldVsyncId=137295788, layerName=12f1b3d com.example.sharenshop/com.example.sharenshop.MainActivity#34711
2025-06-17 06:23:06.821  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295813, oldVsyncId=137295788, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.838  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295838, oldVsyncId=137295813, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.855  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295857, oldVsyncId=137295838, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.871  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295876, oldVsyncId=137295857, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.888  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295895, oldVsyncId=137295876, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.905  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295914, oldVsyncId=137295895, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.922  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295933, oldVsyncId=137295914, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.938  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295952, oldVsyncId=137295933, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:06.955  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295971, oldVsyncId=137295952, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:07.206  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137295984, oldVsyncId=137295971, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:07.723  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137296536, oldVsyncId=137296536, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:08.224  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137296925, oldVsyncId=137296912, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:08.726  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137297315, oldVsyncId=137297302, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:09.227  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137297536, oldVsyncId=137297523, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:09.729  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137297621, oldVsyncId=137297536, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:10.213  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137297868, oldVsyncId=137297621, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:10.714  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137298217, oldVsyncId=137297868, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:11.216  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137298578, oldVsyncId=137298217, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712
2025-06-17 06:23:11.717  1342-1342  MI-SF                   surfaceflinger                       E  MiSurfaceFlingerImpl.cpp.vsyncId=137298951, oldVsyncId=137298578, layerName=com.example.sharenshop/com.example.sharenshop.MainActivity#34712

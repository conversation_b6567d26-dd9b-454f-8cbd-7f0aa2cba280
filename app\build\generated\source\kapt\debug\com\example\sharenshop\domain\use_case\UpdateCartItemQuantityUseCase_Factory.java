// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.CartRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UpdateCartItemQuantityUseCase_Factory implements Factory<UpdateCartItemQuantityUseCase> {
  private final Provider<CartRepository> repositoryProvider;

  public UpdateCartItemQuantityUseCase_Factory(Provider<CartRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public UpdateCartItemQuantityUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static UpdateCartItemQuantityUseCase_Factory create(
      Provider<CartRepository> repositoryProvider) {
    return new UpdateCartItemQuantityUseCase_Factory(repositoryProvider);
  }

  public static UpdateCartItemQuantityUseCase newInstance(CartRepository repository) {
    return new UpdateCartItemQuantityUseCase(repository);
  }
}

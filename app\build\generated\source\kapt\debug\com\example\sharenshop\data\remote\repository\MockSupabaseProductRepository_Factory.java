// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.data.remote.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MockSupabaseProductRepository_Factory implements Factory<MockSupabaseProductRepository> {
  @Override
  public MockSupabaseProductRepository get() {
    return newInstance();
  }

  public static MockSupabaseProductRepository_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static MockSupabaseProductRepository newInstance() {
    return new MockSupabaseProductRepository();
  }

  private static final class InstanceHolder {
    private static final MockSupabaseProductRepository_Factory INSTANCE = new MockSupabaseProductRepository_Factory();
  }
}

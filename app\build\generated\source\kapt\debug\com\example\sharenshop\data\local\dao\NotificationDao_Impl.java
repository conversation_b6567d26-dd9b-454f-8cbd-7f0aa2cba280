package com.example.sharenshop.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.sharenshop.data.model.Notification;
import com.example.sharenshop.data.model.NotificationCategory;
import com.example.sharenshop.data.model.NotificationPriority;
import com.example.sharenshop.data.model.NotificationType;
import java.lang.Class;
import java.lang.Exception;
import java.lang.IllegalArgumentException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class NotificationDao_Impl implements NotificationDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Notification> __insertionAdapterOfNotification;

  private final EntityDeletionOrUpdateAdapter<Notification> __deletionAdapterOfNotification;

  private final EntityDeletionOrUpdateAdapter<Notification> __updateAdapterOfNotification;

  private final SharedSQLiteStatement __preparedStmtOfMarkAsRead;

  private final SharedSQLiteStatement __preparedStmtOfMarkAllAsReadForUser;

  private final SharedSQLiteStatement __preparedStmtOfMarkAsReadByType;

  private final SharedSQLiteStatement __preparedStmtOfDeleteReadNotifications;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldNotifications;

  public NotificationDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfNotification = new EntityInsertionAdapter<Notification>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `notifications` (`id`,`recipientId`,`senderId`,`type`,`category`,`priority`,`title`,`message`,`actionData`,`relatedEntityId`,`relatedEntityType`,`isRead`,`isActionRequired`,`isActionTaken`,`actionResult`,`createdAt`,`readAt`,`actionTakenAt`,`expiresAt`,`smsRequired`,`smsSent`,`smsDelivered`,`smsFailureReason`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Notification entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getRecipientId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getRecipientId());
        }
        if (entity.getSenderId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getSenderId());
        }
        statement.bindString(4, __NotificationType_enumToString(entity.getType()));
        statement.bindString(5, __NotificationCategory_enumToString(entity.getCategory()));
        statement.bindString(6, __NotificationPriority_enumToString(entity.getPriority()));
        if (entity.getTitle() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getTitle());
        }
        if (entity.getMessage() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getMessage());
        }
        if (entity.getActionData() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getActionData());
        }
        if (entity.getRelatedEntityId() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getRelatedEntityId());
        }
        if (entity.getRelatedEntityType() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getRelatedEntityType());
        }
        final int _tmp = entity.isRead() ? 1 : 0;
        statement.bindLong(12, _tmp);
        final int _tmp_1 = entity.isActionRequired() ? 1 : 0;
        statement.bindLong(13, _tmp_1);
        final int _tmp_2 = entity.isActionTaken() ? 1 : 0;
        statement.bindLong(14, _tmp_2);
        if (entity.getActionResult() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getActionResult());
        }
        statement.bindLong(16, entity.getCreatedAt());
        if (entity.getReadAt() == null) {
          statement.bindNull(17);
        } else {
          statement.bindLong(17, entity.getReadAt());
        }
        if (entity.getActionTakenAt() == null) {
          statement.bindNull(18);
        } else {
          statement.bindLong(18, entity.getActionTakenAt());
        }
        if (entity.getExpiresAt() == null) {
          statement.bindNull(19);
        } else {
          statement.bindLong(19, entity.getExpiresAt());
        }
        final int _tmp_3 = entity.getSmsRequired() ? 1 : 0;
        statement.bindLong(20, _tmp_3);
        final int _tmp_4 = entity.getSmsSent() ? 1 : 0;
        statement.bindLong(21, _tmp_4);
        final int _tmp_5 = entity.getSmsDelivered() ? 1 : 0;
        statement.bindLong(22, _tmp_5);
        if (entity.getSmsFailureReason() == null) {
          statement.bindNull(23);
        } else {
          statement.bindString(23, entity.getSmsFailureReason());
        }
      }
    };
    this.__deletionAdapterOfNotification = new EntityDeletionOrUpdateAdapter<Notification>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `notifications` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Notification entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
      }
    };
    this.__updateAdapterOfNotification = new EntityDeletionOrUpdateAdapter<Notification>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `notifications` SET `id` = ?,`recipientId` = ?,`senderId` = ?,`type` = ?,`category` = ?,`priority` = ?,`title` = ?,`message` = ?,`actionData` = ?,`relatedEntityId` = ?,`relatedEntityType` = ?,`isRead` = ?,`isActionRequired` = ?,`isActionTaken` = ?,`actionResult` = ?,`createdAt` = ?,`readAt` = ?,`actionTakenAt` = ?,`expiresAt` = ?,`smsRequired` = ?,`smsSent` = ?,`smsDelivered` = ?,`smsFailureReason` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Notification entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getRecipientId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getRecipientId());
        }
        if (entity.getSenderId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getSenderId());
        }
        statement.bindString(4, __NotificationType_enumToString(entity.getType()));
        statement.bindString(5, __NotificationCategory_enumToString(entity.getCategory()));
        statement.bindString(6, __NotificationPriority_enumToString(entity.getPriority()));
        if (entity.getTitle() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getTitle());
        }
        if (entity.getMessage() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getMessage());
        }
        if (entity.getActionData() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getActionData());
        }
        if (entity.getRelatedEntityId() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getRelatedEntityId());
        }
        if (entity.getRelatedEntityType() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getRelatedEntityType());
        }
        final int _tmp = entity.isRead() ? 1 : 0;
        statement.bindLong(12, _tmp);
        final int _tmp_1 = entity.isActionRequired() ? 1 : 0;
        statement.bindLong(13, _tmp_1);
        final int _tmp_2 = entity.isActionTaken() ? 1 : 0;
        statement.bindLong(14, _tmp_2);
        if (entity.getActionResult() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getActionResult());
        }
        statement.bindLong(16, entity.getCreatedAt());
        if (entity.getReadAt() == null) {
          statement.bindNull(17);
        } else {
          statement.bindLong(17, entity.getReadAt());
        }
        if (entity.getActionTakenAt() == null) {
          statement.bindNull(18);
        } else {
          statement.bindLong(18, entity.getActionTakenAt());
        }
        if (entity.getExpiresAt() == null) {
          statement.bindNull(19);
        } else {
          statement.bindLong(19, entity.getExpiresAt());
        }
        final int _tmp_3 = entity.getSmsRequired() ? 1 : 0;
        statement.bindLong(20, _tmp_3);
        final int _tmp_4 = entity.getSmsSent() ? 1 : 0;
        statement.bindLong(21, _tmp_4);
        final int _tmp_5 = entity.getSmsDelivered() ? 1 : 0;
        statement.bindLong(22, _tmp_5);
        if (entity.getSmsFailureReason() == null) {
          statement.bindNull(23);
        } else {
          statement.bindString(23, entity.getSmsFailureReason());
        }
        if (entity.getId() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getId());
        }
      }
    };
    this.__preparedStmtOfMarkAsRead = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE notifications SET isRead = 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfMarkAllAsReadForUser = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE notifications SET isRead = 1 WHERE recipientId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfMarkAsReadByType = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE notifications SET isRead = 1 WHERE recipientId = ? AND type = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteReadNotifications = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM notifications WHERE recipientId = ? AND isRead = 1";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOldNotifications = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM notifications WHERE createdAt < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertNotification(final Notification notification,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfNotification.insert(notification);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertNotifications(final List<Notification> notifications,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfNotification.insert(notifications);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteNotification(final Notification notification,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfNotification.handle(notification);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateNotification(final Notification notification,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfNotification.handle(notification);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object markAsRead(final String notificationId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfMarkAsRead.acquire();
        int _argIndex = 1;
        if (notificationId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, notificationId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfMarkAsRead.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object markAllAsReadForUser(final String userId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfMarkAllAsReadForUser.acquire();
        int _argIndex = 1;
        if (userId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, userId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfMarkAllAsReadForUser.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object markAsReadByType(final String userId, final NotificationType type,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfMarkAsReadByType.acquire();
        int _argIndex = 1;
        if (userId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, userId);
        }
        _argIndex = 2;
        _stmt.bindString(_argIndex, __NotificationType_enumToString(type));
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfMarkAsReadByType.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteReadNotifications(final String userId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteReadNotifications.acquire();
        int _argIndex = 1;
        if (userId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, userId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteReadNotifications.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldNotifications(final long expireDate,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldNotifications.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, expireDate);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldNotifications.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getNotificationById(final String id,
      final Continuation<? super Notification> $completion) {
    final String _sql = "SELECT * FROM notifications WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (id == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, id);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Notification>() {
      @Override
      @Nullable
      public Notification call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfRecipientId = CursorUtil.getColumnIndexOrThrow(_cursor, "recipientId");
          final int _cursorIndexOfSenderId = CursorUtil.getColumnIndexOrThrow(_cursor, "senderId");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "message");
          final int _cursorIndexOfActionData = CursorUtil.getColumnIndexOrThrow(_cursor, "actionData");
          final int _cursorIndexOfRelatedEntityId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedEntityId");
          final int _cursorIndexOfRelatedEntityType = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedEntityType");
          final int _cursorIndexOfIsRead = CursorUtil.getColumnIndexOrThrow(_cursor, "isRead");
          final int _cursorIndexOfIsActionRequired = CursorUtil.getColumnIndexOrThrow(_cursor, "isActionRequired");
          final int _cursorIndexOfIsActionTaken = CursorUtil.getColumnIndexOrThrow(_cursor, "isActionTaken");
          final int _cursorIndexOfActionResult = CursorUtil.getColumnIndexOrThrow(_cursor, "actionResult");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfReadAt = CursorUtil.getColumnIndexOrThrow(_cursor, "readAt");
          final int _cursorIndexOfActionTakenAt = CursorUtil.getColumnIndexOrThrow(_cursor, "actionTakenAt");
          final int _cursorIndexOfExpiresAt = CursorUtil.getColumnIndexOrThrow(_cursor, "expiresAt");
          final int _cursorIndexOfSmsRequired = CursorUtil.getColumnIndexOrThrow(_cursor, "smsRequired");
          final int _cursorIndexOfSmsSent = CursorUtil.getColumnIndexOrThrow(_cursor, "smsSent");
          final int _cursorIndexOfSmsDelivered = CursorUtil.getColumnIndexOrThrow(_cursor, "smsDelivered");
          final int _cursorIndexOfSmsFailureReason = CursorUtil.getColumnIndexOrThrow(_cursor, "smsFailureReason");
          final Notification _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpRecipientId;
            if (_cursor.isNull(_cursorIndexOfRecipientId)) {
              _tmpRecipientId = null;
            } else {
              _tmpRecipientId = _cursor.getString(_cursorIndexOfRecipientId);
            }
            final String _tmpSenderId;
            if (_cursor.isNull(_cursorIndexOfSenderId)) {
              _tmpSenderId = null;
            } else {
              _tmpSenderId = _cursor.getString(_cursorIndexOfSenderId);
            }
            final NotificationType _tmpType;
            _tmpType = __NotificationType_stringToEnum(_cursor.getString(_cursorIndexOfType));
            final NotificationCategory _tmpCategory;
            _tmpCategory = __NotificationCategory_stringToEnum(_cursor.getString(_cursorIndexOfCategory));
            final NotificationPriority _tmpPriority;
            _tmpPriority = __NotificationPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpMessage;
            if (_cursor.isNull(_cursorIndexOfMessage)) {
              _tmpMessage = null;
            } else {
              _tmpMessage = _cursor.getString(_cursorIndexOfMessage);
            }
            final String _tmpActionData;
            if (_cursor.isNull(_cursorIndexOfActionData)) {
              _tmpActionData = null;
            } else {
              _tmpActionData = _cursor.getString(_cursorIndexOfActionData);
            }
            final String _tmpRelatedEntityId;
            if (_cursor.isNull(_cursorIndexOfRelatedEntityId)) {
              _tmpRelatedEntityId = null;
            } else {
              _tmpRelatedEntityId = _cursor.getString(_cursorIndexOfRelatedEntityId);
            }
            final String _tmpRelatedEntityType;
            if (_cursor.isNull(_cursorIndexOfRelatedEntityType)) {
              _tmpRelatedEntityType = null;
            } else {
              _tmpRelatedEntityType = _cursor.getString(_cursorIndexOfRelatedEntityType);
            }
            final boolean _tmpIsRead;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRead);
            _tmpIsRead = _tmp != 0;
            final boolean _tmpIsActionRequired;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActionRequired);
            _tmpIsActionRequired = _tmp_1 != 0;
            final boolean _tmpIsActionTaken;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActionTaken);
            _tmpIsActionTaken = _tmp_2 != 0;
            final String _tmpActionResult;
            if (_cursor.isNull(_cursorIndexOfActionResult)) {
              _tmpActionResult = null;
            } else {
              _tmpActionResult = _cursor.getString(_cursorIndexOfActionResult);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpReadAt;
            if (_cursor.isNull(_cursorIndexOfReadAt)) {
              _tmpReadAt = null;
            } else {
              _tmpReadAt = _cursor.getLong(_cursorIndexOfReadAt);
            }
            final Long _tmpActionTakenAt;
            if (_cursor.isNull(_cursorIndexOfActionTakenAt)) {
              _tmpActionTakenAt = null;
            } else {
              _tmpActionTakenAt = _cursor.getLong(_cursorIndexOfActionTakenAt);
            }
            final Long _tmpExpiresAt;
            if (_cursor.isNull(_cursorIndexOfExpiresAt)) {
              _tmpExpiresAt = null;
            } else {
              _tmpExpiresAt = _cursor.getLong(_cursorIndexOfExpiresAt);
            }
            final boolean _tmpSmsRequired;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfSmsRequired);
            _tmpSmsRequired = _tmp_3 != 0;
            final boolean _tmpSmsSent;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfSmsSent);
            _tmpSmsSent = _tmp_4 != 0;
            final boolean _tmpSmsDelivered;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfSmsDelivered);
            _tmpSmsDelivered = _tmp_5 != 0;
            final String _tmpSmsFailureReason;
            if (_cursor.isNull(_cursorIndexOfSmsFailureReason)) {
              _tmpSmsFailureReason = null;
            } else {
              _tmpSmsFailureReason = _cursor.getString(_cursorIndexOfSmsFailureReason);
            }
            _result = new Notification(_tmpId,_tmpRecipientId,_tmpSenderId,_tmpType,_tmpCategory,_tmpPriority,_tmpTitle,_tmpMessage,_tmpActionData,_tmpRelatedEntityId,_tmpRelatedEntityType,_tmpIsRead,_tmpIsActionRequired,_tmpIsActionTaken,_tmpActionResult,_tmpCreatedAt,_tmpReadAt,_tmpActionTakenAt,_tmpExpiresAt,_tmpSmsRequired,_tmpSmsSent,_tmpSmsDelivered,_tmpSmsFailureReason);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Notification>> getNotificationsByUser(final String userId) {
    final String _sql = "SELECT * FROM notifications WHERE recipientId = ? ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"notifications"}, new Callable<List<Notification>>() {
      @Override
      @NonNull
      public List<Notification> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfRecipientId = CursorUtil.getColumnIndexOrThrow(_cursor, "recipientId");
          final int _cursorIndexOfSenderId = CursorUtil.getColumnIndexOrThrow(_cursor, "senderId");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "message");
          final int _cursorIndexOfActionData = CursorUtil.getColumnIndexOrThrow(_cursor, "actionData");
          final int _cursorIndexOfRelatedEntityId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedEntityId");
          final int _cursorIndexOfRelatedEntityType = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedEntityType");
          final int _cursorIndexOfIsRead = CursorUtil.getColumnIndexOrThrow(_cursor, "isRead");
          final int _cursorIndexOfIsActionRequired = CursorUtil.getColumnIndexOrThrow(_cursor, "isActionRequired");
          final int _cursorIndexOfIsActionTaken = CursorUtil.getColumnIndexOrThrow(_cursor, "isActionTaken");
          final int _cursorIndexOfActionResult = CursorUtil.getColumnIndexOrThrow(_cursor, "actionResult");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfReadAt = CursorUtil.getColumnIndexOrThrow(_cursor, "readAt");
          final int _cursorIndexOfActionTakenAt = CursorUtil.getColumnIndexOrThrow(_cursor, "actionTakenAt");
          final int _cursorIndexOfExpiresAt = CursorUtil.getColumnIndexOrThrow(_cursor, "expiresAt");
          final int _cursorIndexOfSmsRequired = CursorUtil.getColumnIndexOrThrow(_cursor, "smsRequired");
          final int _cursorIndexOfSmsSent = CursorUtil.getColumnIndexOrThrow(_cursor, "smsSent");
          final int _cursorIndexOfSmsDelivered = CursorUtil.getColumnIndexOrThrow(_cursor, "smsDelivered");
          final int _cursorIndexOfSmsFailureReason = CursorUtil.getColumnIndexOrThrow(_cursor, "smsFailureReason");
          final List<Notification> _result = new ArrayList<Notification>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Notification _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpRecipientId;
            if (_cursor.isNull(_cursorIndexOfRecipientId)) {
              _tmpRecipientId = null;
            } else {
              _tmpRecipientId = _cursor.getString(_cursorIndexOfRecipientId);
            }
            final String _tmpSenderId;
            if (_cursor.isNull(_cursorIndexOfSenderId)) {
              _tmpSenderId = null;
            } else {
              _tmpSenderId = _cursor.getString(_cursorIndexOfSenderId);
            }
            final NotificationType _tmpType;
            _tmpType = __NotificationType_stringToEnum(_cursor.getString(_cursorIndexOfType));
            final NotificationCategory _tmpCategory;
            _tmpCategory = __NotificationCategory_stringToEnum(_cursor.getString(_cursorIndexOfCategory));
            final NotificationPriority _tmpPriority;
            _tmpPriority = __NotificationPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpMessage;
            if (_cursor.isNull(_cursorIndexOfMessage)) {
              _tmpMessage = null;
            } else {
              _tmpMessage = _cursor.getString(_cursorIndexOfMessage);
            }
            final String _tmpActionData;
            if (_cursor.isNull(_cursorIndexOfActionData)) {
              _tmpActionData = null;
            } else {
              _tmpActionData = _cursor.getString(_cursorIndexOfActionData);
            }
            final String _tmpRelatedEntityId;
            if (_cursor.isNull(_cursorIndexOfRelatedEntityId)) {
              _tmpRelatedEntityId = null;
            } else {
              _tmpRelatedEntityId = _cursor.getString(_cursorIndexOfRelatedEntityId);
            }
            final String _tmpRelatedEntityType;
            if (_cursor.isNull(_cursorIndexOfRelatedEntityType)) {
              _tmpRelatedEntityType = null;
            } else {
              _tmpRelatedEntityType = _cursor.getString(_cursorIndexOfRelatedEntityType);
            }
            final boolean _tmpIsRead;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRead);
            _tmpIsRead = _tmp != 0;
            final boolean _tmpIsActionRequired;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActionRequired);
            _tmpIsActionRequired = _tmp_1 != 0;
            final boolean _tmpIsActionTaken;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActionTaken);
            _tmpIsActionTaken = _tmp_2 != 0;
            final String _tmpActionResult;
            if (_cursor.isNull(_cursorIndexOfActionResult)) {
              _tmpActionResult = null;
            } else {
              _tmpActionResult = _cursor.getString(_cursorIndexOfActionResult);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpReadAt;
            if (_cursor.isNull(_cursorIndexOfReadAt)) {
              _tmpReadAt = null;
            } else {
              _tmpReadAt = _cursor.getLong(_cursorIndexOfReadAt);
            }
            final Long _tmpActionTakenAt;
            if (_cursor.isNull(_cursorIndexOfActionTakenAt)) {
              _tmpActionTakenAt = null;
            } else {
              _tmpActionTakenAt = _cursor.getLong(_cursorIndexOfActionTakenAt);
            }
            final Long _tmpExpiresAt;
            if (_cursor.isNull(_cursorIndexOfExpiresAt)) {
              _tmpExpiresAt = null;
            } else {
              _tmpExpiresAt = _cursor.getLong(_cursorIndexOfExpiresAt);
            }
            final boolean _tmpSmsRequired;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfSmsRequired);
            _tmpSmsRequired = _tmp_3 != 0;
            final boolean _tmpSmsSent;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfSmsSent);
            _tmpSmsSent = _tmp_4 != 0;
            final boolean _tmpSmsDelivered;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfSmsDelivered);
            _tmpSmsDelivered = _tmp_5 != 0;
            final String _tmpSmsFailureReason;
            if (_cursor.isNull(_cursorIndexOfSmsFailureReason)) {
              _tmpSmsFailureReason = null;
            } else {
              _tmpSmsFailureReason = _cursor.getString(_cursorIndexOfSmsFailureReason);
            }
            _item = new Notification(_tmpId,_tmpRecipientId,_tmpSenderId,_tmpType,_tmpCategory,_tmpPriority,_tmpTitle,_tmpMessage,_tmpActionData,_tmpRelatedEntityId,_tmpRelatedEntityType,_tmpIsRead,_tmpIsActionRequired,_tmpIsActionTaken,_tmpActionResult,_tmpCreatedAt,_tmpReadAt,_tmpActionTakenAt,_tmpExpiresAt,_tmpSmsRequired,_tmpSmsSent,_tmpSmsDelivered,_tmpSmsFailureReason);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Notification>> getUnreadNotificationsByUser(final String userId) {
    final String _sql = "SELECT * FROM notifications WHERE recipientId = ? AND isRead = 0 ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"notifications"}, new Callable<List<Notification>>() {
      @Override
      @NonNull
      public List<Notification> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfRecipientId = CursorUtil.getColumnIndexOrThrow(_cursor, "recipientId");
          final int _cursorIndexOfSenderId = CursorUtil.getColumnIndexOrThrow(_cursor, "senderId");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "message");
          final int _cursorIndexOfActionData = CursorUtil.getColumnIndexOrThrow(_cursor, "actionData");
          final int _cursorIndexOfRelatedEntityId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedEntityId");
          final int _cursorIndexOfRelatedEntityType = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedEntityType");
          final int _cursorIndexOfIsRead = CursorUtil.getColumnIndexOrThrow(_cursor, "isRead");
          final int _cursorIndexOfIsActionRequired = CursorUtil.getColumnIndexOrThrow(_cursor, "isActionRequired");
          final int _cursorIndexOfIsActionTaken = CursorUtil.getColumnIndexOrThrow(_cursor, "isActionTaken");
          final int _cursorIndexOfActionResult = CursorUtil.getColumnIndexOrThrow(_cursor, "actionResult");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfReadAt = CursorUtil.getColumnIndexOrThrow(_cursor, "readAt");
          final int _cursorIndexOfActionTakenAt = CursorUtil.getColumnIndexOrThrow(_cursor, "actionTakenAt");
          final int _cursorIndexOfExpiresAt = CursorUtil.getColumnIndexOrThrow(_cursor, "expiresAt");
          final int _cursorIndexOfSmsRequired = CursorUtil.getColumnIndexOrThrow(_cursor, "smsRequired");
          final int _cursorIndexOfSmsSent = CursorUtil.getColumnIndexOrThrow(_cursor, "smsSent");
          final int _cursorIndexOfSmsDelivered = CursorUtil.getColumnIndexOrThrow(_cursor, "smsDelivered");
          final int _cursorIndexOfSmsFailureReason = CursorUtil.getColumnIndexOrThrow(_cursor, "smsFailureReason");
          final List<Notification> _result = new ArrayList<Notification>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Notification _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpRecipientId;
            if (_cursor.isNull(_cursorIndexOfRecipientId)) {
              _tmpRecipientId = null;
            } else {
              _tmpRecipientId = _cursor.getString(_cursorIndexOfRecipientId);
            }
            final String _tmpSenderId;
            if (_cursor.isNull(_cursorIndexOfSenderId)) {
              _tmpSenderId = null;
            } else {
              _tmpSenderId = _cursor.getString(_cursorIndexOfSenderId);
            }
            final NotificationType _tmpType;
            _tmpType = __NotificationType_stringToEnum(_cursor.getString(_cursorIndexOfType));
            final NotificationCategory _tmpCategory;
            _tmpCategory = __NotificationCategory_stringToEnum(_cursor.getString(_cursorIndexOfCategory));
            final NotificationPriority _tmpPriority;
            _tmpPriority = __NotificationPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpMessage;
            if (_cursor.isNull(_cursorIndexOfMessage)) {
              _tmpMessage = null;
            } else {
              _tmpMessage = _cursor.getString(_cursorIndexOfMessage);
            }
            final String _tmpActionData;
            if (_cursor.isNull(_cursorIndexOfActionData)) {
              _tmpActionData = null;
            } else {
              _tmpActionData = _cursor.getString(_cursorIndexOfActionData);
            }
            final String _tmpRelatedEntityId;
            if (_cursor.isNull(_cursorIndexOfRelatedEntityId)) {
              _tmpRelatedEntityId = null;
            } else {
              _tmpRelatedEntityId = _cursor.getString(_cursorIndexOfRelatedEntityId);
            }
            final String _tmpRelatedEntityType;
            if (_cursor.isNull(_cursorIndexOfRelatedEntityType)) {
              _tmpRelatedEntityType = null;
            } else {
              _tmpRelatedEntityType = _cursor.getString(_cursorIndexOfRelatedEntityType);
            }
            final boolean _tmpIsRead;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRead);
            _tmpIsRead = _tmp != 0;
            final boolean _tmpIsActionRequired;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActionRequired);
            _tmpIsActionRequired = _tmp_1 != 0;
            final boolean _tmpIsActionTaken;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActionTaken);
            _tmpIsActionTaken = _tmp_2 != 0;
            final String _tmpActionResult;
            if (_cursor.isNull(_cursorIndexOfActionResult)) {
              _tmpActionResult = null;
            } else {
              _tmpActionResult = _cursor.getString(_cursorIndexOfActionResult);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpReadAt;
            if (_cursor.isNull(_cursorIndexOfReadAt)) {
              _tmpReadAt = null;
            } else {
              _tmpReadAt = _cursor.getLong(_cursorIndexOfReadAt);
            }
            final Long _tmpActionTakenAt;
            if (_cursor.isNull(_cursorIndexOfActionTakenAt)) {
              _tmpActionTakenAt = null;
            } else {
              _tmpActionTakenAt = _cursor.getLong(_cursorIndexOfActionTakenAt);
            }
            final Long _tmpExpiresAt;
            if (_cursor.isNull(_cursorIndexOfExpiresAt)) {
              _tmpExpiresAt = null;
            } else {
              _tmpExpiresAt = _cursor.getLong(_cursorIndexOfExpiresAt);
            }
            final boolean _tmpSmsRequired;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfSmsRequired);
            _tmpSmsRequired = _tmp_3 != 0;
            final boolean _tmpSmsSent;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfSmsSent);
            _tmpSmsSent = _tmp_4 != 0;
            final boolean _tmpSmsDelivered;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfSmsDelivered);
            _tmpSmsDelivered = _tmp_5 != 0;
            final String _tmpSmsFailureReason;
            if (_cursor.isNull(_cursorIndexOfSmsFailureReason)) {
              _tmpSmsFailureReason = null;
            } else {
              _tmpSmsFailureReason = _cursor.getString(_cursorIndexOfSmsFailureReason);
            }
            _item = new Notification(_tmpId,_tmpRecipientId,_tmpSenderId,_tmpType,_tmpCategory,_tmpPriority,_tmpTitle,_tmpMessage,_tmpActionData,_tmpRelatedEntityId,_tmpRelatedEntityType,_tmpIsRead,_tmpIsActionRequired,_tmpIsActionTaken,_tmpActionResult,_tmpCreatedAt,_tmpReadAt,_tmpActionTakenAt,_tmpExpiresAt,_tmpSmsRequired,_tmpSmsSent,_tmpSmsDelivered,_tmpSmsFailureReason);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Notification>> getNotificationsByUserAndType(final String userId,
      final NotificationType type) {
    final String _sql = "SELECT * FROM notifications WHERE recipientId = ? AND type = ? ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    _argIndex = 2;
    _statement.bindString(_argIndex, __NotificationType_enumToString(type));
    return CoroutinesRoom.createFlow(__db, false, new String[] {"notifications"}, new Callable<List<Notification>>() {
      @Override
      @NonNull
      public List<Notification> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfRecipientId = CursorUtil.getColumnIndexOrThrow(_cursor, "recipientId");
          final int _cursorIndexOfSenderId = CursorUtil.getColumnIndexOrThrow(_cursor, "senderId");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "message");
          final int _cursorIndexOfActionData = CursorUtil.getColumnIndexOrThrow(_cursor, "actionData");
          final int _cursorIndexOfRelatedEntityId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedEntityId");
          final int _cursorIndexOfRelatedEntityType = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedEntityType");
          final int _cursorIndexOfIsRead = CursorUtil.getColumnIndexOrThrow(_cursor, "isRead");
          final int _cursorIndexOfIsActionRequired = CursorUtil.getColumnIndexOrThrow(_cursor, "isActionRequired");
          final int _cursorIndexOfIsActionTaken = CursorUtil.getColumnIndexOrThrow(_cursor, "isActionTaken");
          final int _cursorIndexOfActionResult = CursorUtil.getColumnIndexOrThrow(_cursor, "actionResult");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfReadAt = CursorUtil.getColumnIndexOrThrow(_cursor, "readAt");
          final int _cursorIndexOfActionTakenAt = CursorUtil.getColumnIndexOrThrow(_cursor, "actionTakenAt");
          final int _cursorIndexOfExpiresAt = CursorUtil.getColumnIndexOrThrow(_cursor, "expiresAt");
          final int _cursorIndexOfSmsRequired = CursorUtil.getColumnIndexOrThrow(_cursor, "smsRequired");
          final int _cursorIndexOfSmsSent = CursorUtil.getColumnIndexOrThrow(_cursor, "smsSent");
          final int _cursorIndexOfSmsDelivered = CursorUtil.getColumnIndexOrThrow(_cursor, "smsDelivered");
          final int _cursorIndexOfSmsFailureReason = CursorUtil.getColumnIndexOrThrow(_cursor, "smsFailureReason");
          final List<Notification> _result = new ArrayList<Notification>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Notification _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpRecipientId;
            if (_cursor.isNull(_cursorIndexOfRecipientId)) {
              _tmpRecipientId = null;
            } else {
              _tmpRecipientId = _cursor.getString(_cursorIndexOfRecipientId);
            }
            final String _tmpSenderId;
            if (_cursor.isNull(_cursorIndexOfSenderId)) {
              _tmpSenderId = null;
            } else {
              _tmpSenderId = _cursor.getString(_cursorIndexOfSenderId);
            }
            final NotificationType _tmpType;
            _tmpType = __NotificationType_stringToEnum(_cursor.getString(_cursorIndexOfType));
            final NotificationCategory _tmpCategory;
            _tmpCategory = __NotificationCategory_stringToEnum(_cursor.getString(_cursorIndexOfCategory));
            final NotificationPriority _tmpPriority;
            _tmpPriority = __NotificationPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpMessage;
            if (_cursor.isNull(_cursorIndexOfMessage)) {
              _tmpMessage = null;
            } else {
              _tmpMessage = _cursor.getString(_cursorIndexOfMessage);
            }
            final String _tmpActionData;
            if (_cursor.isNull(_cursorIndexOfActionData)) {
              _tmpActionData = null;
            } else {
              _tmpActionData = _cursor.getString(_cursorIndexOfActionData);
            }
            final String _tmpRelatedEntityId;
            if (_cursor.isNull(_cursorIndexOfRelatedEntityId)) {
              _tmpRelatedEntityId = null;
            } else {
              _tmpRelatedEntityId = _cursor.getString(_cursorIndexOfRelatedEntityId);
            }
            final String _tmpRelatedEntityType;
            if (_cursor.isNull(_cursorIndexOfRelatedEntityType)) {
              _tmpRelatedEntityType = null;
            } else {
              _tmpRelatedEntityType = _cursor.getString(_cursorIndexOfRelatedEntityType);
            }
            final boolean _tmpIsRead;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRead);
            _tmpIsRead = _tmp != 0;
            final boolean _tmpIsActionRequired;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActionRequired);
            _tmpIsActionRequired = _tmp_1 != 0;
            final boolean _tmpIsActionTaken;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActionTaken);
            _tmpIsActionTaken = _tmp_2 != 0;
            final String _tmpActionResult;
            if (_cursor.isNull(_cursorIndexOfActionResult)) {
              _tmpActionResult = null;
            } else {
              _tmpActionResult = _cursor.getString(_cursorIndexOfActionResult);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpReadAt;
            if (_cursor.isNull(_cursorIndexOfReadAt)) {
              _tmpReadAt = null;
            } else {
              _tmpReadAt = _cursor.getLong(_cursorIndexOfReadAt);
            }
            final Long _tmpActionTakenAt;
            if (_cursor.isNull(_cursorIndexOfActionTakenAt)) {
              _tmpActionTakenAt = null;
            } else {
              _tmpActionTakenAt = _cursor.getLong(_cursorIndexOfActionTakenAt);
            }
            final Long _tmpExpiresAt;
            if (_cursor.isNull(_cursorIndexOfExpiresAt)) {
              _tmpExpiresAt = null;
            } else {
              _tmpExpiresAt = _cursor.getLong(_cursorIndexOfExpiresAt);
            }
            final boolean _tmpSmsRequired;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfSmsRequired);
            _tmpSmsRequired = _tmp_3 != 0;
            final boolean _tmpSmsSent;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfSmsSent);
            _tmpSmsSent = _tmp_4 != 0;
            final boolean _tmpSmsDelivered;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfSmsDelivered);
            _tmpSmsDelivered = _tmp_5 != 0;
            final String _tmpSmsFailureReason;
            if (_cursor.isNull(_cursorIndexOfSmsFailureReason)) {
              _tmpSmsFailureReason = null;
            } else {
              _tmpSmsFailureReason = _cursor.getString(_cursorIndexOfSmsFailureReason);
            }
            _item = new Notification(_tmpId,_tmpRecipientId,_tmpSenderId,_tmpType,_tmpCategory,_tmpPriority,_tmpTitle,_tmpMessage,_tmpActionData,_tmpRelatedEntityId,_tmpRelatedEntityType,_tmpIsRead,_tmpIsActionRequired,_tmpIsActionTaken,_tmpActionResult,_tmpCreatedAt,_tmpReadAt,_tmpActionTakenAt,_tmpExpiresAt,_tmpSmsRequired,_tmpSmsSent,_tmpSmsDelivered,_tmpSmsFailureReason);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getUnreadNotificationsCount(final String userId,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM notifications WHERE recipientId = ? AND isRead = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getRecentNotifications(final String userId, final int limit,
      final Continuation<? super List<Notification>> $completion) {
    final String _sql = "SELECT * FROM notifications WHERE recipientId = ? ORDER BY createdAt DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Notification>>() {
      @Override
      @NonNull
      public List<Notification> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfRecipientId = CursorUtil.getColumnIndexOrThrow(_cursor, "recipientId");
          final int _cursorIndexOfSenderId = CursorUtil.getColumnIndexOrThrow(_cursor, "senderId");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "message");
          final int _cursorIndexOfActionData = CursorUtil.getColumnIndexOrThrow(_cursor, "actionData");
          final int _cursorIndexOfRelatedEntityId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedEntityId");
          final int _cursorIndexOfRelatedEntityType = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedEntityType");
          final int _cursorIndexOfIsRead = CursorUtil.getColumnIndexOrThrow(_cursor, "isRead");
          final int _cursorIndexOfIsActionRequired = CursorUtil.getColumnIndexOrThrow(_cursor, "isActionRequired");
          final int _cursorIndexOfIsActionTaken = CursorUtil.getColumnIndexOrThrow(_cursor, "isActionTaken");
          final int _cursorIndexOfActionResult = CursorUtil.getColumnIndexOrThrow(_cursor, "actionResult");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfReadAt = CursorUtil.getColumnIndexOrThrow(_cursor, "readAt");
          final int _cursorIndexOfActionTakenAt = CursorUtil.getColumnIndexOrThrow(_cursor, "actionTakenAt");
          final int _cursorIndexOfExpiresAt = CursorUtil.getColumnIndexOrThrow(_cursor, "expiresAt");
          final int _cursorIndexOfSmsRequired = CursorUtil.getColumnIndexOrThrow(_cursor, "smsRequired");
          final int _cursorIndexOfSmsSent = CursorUtil.getColumnIndexOrThrow(_cursor, "smsSent");
          final int _cursorIndexOfSmsDelivered = CursorUtil.getColumnIndexOrThrow(_cursor, "smsDelivered");
          final int _cursorIndexOfSmsFailureReason = CursorUtil.getColumnIndexOrThrow(_cursor, "smsFailureReason");
          final List<Notification> _result = new ArrayList<Notification>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Notification _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpRecipientId;
            if (_cursor.isNull(_cursorIndexOfRecipientId)) {
              _tmpRecipientId = null;
            } else {
              _tmpRecipientId = _cursor.getString(_cursorIndexOfRecipientId);
            }
            final String _tmpSenderId;
            if (_cursor.isNull(_cursorIndexOfSenderId)) {
              _tmpSenderId = null;
            } else {
              _tmpSenderId = _cursor.getString(_cursorIndexOfSenderId);
            }
            final NotificationType _tmpType;
            _tmpType = __NotificationType_stringToEnum(_cursor.getString(_cursorIndexOfType));
            final NotificationCategory _tmpCategory;
            _tmpCategory = __NotificationCategory_stringToEnum(_cursor.getString(_cursorIndexOfCategory));
            final NotificationPriority _tmpPriority;
            _tmpPriority = __NotificationPriority_stringToEnum(_cursor.getString(_cursorIndexOfPriority));
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpMessage;
            if (_cursor.isNull(_cursorIndexOfMessage)) {
              _tmpMessage = null;
            } else {
              _tmpMessage = _cursor.getString(_cursorIndexOfMessage);
            }
            final String _tmpActionData;
            if (_cursor.isNull(_cursorIndexOfActionData)) {
              _tmpActionData = null;
            } else {
              _tmpActionData = _cursor.getString(_cursorIndexOfActionData);
            }
            final String _tmpRelatedEntityId;
            if (_cursor.isNull(_cursorIndexOfRelatedEntityId)) {
              _tmpRelatedEntityId = null;
            } else {
              _tmpRelatedEntityId = _cursor.getString(_cursorIndexOfRelatedEntityId);
            }
            final String _tmpRelatedEntityType;
            if (_cursor.isNull(_cursorIndexOfRelatedEntityType)) {
              _tmpRelatedEntityType = null;
            } else {
              _tmpRelatedEntityType = _cursor.getString(_cursorIndexOfRelatedEntityType);
            }
            final boolean _tmpIsRead;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRead);
            _tmpIsRead = _tmp != 0;
            final boolean _tmpIsActionRequired;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActionRequired);
            _tmpIsActionRequired = _tmp_1 != 0;
            final boolean _tmpIsActionTaken;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActionTaken);
            _tmpIsActionTaken = _tmp_2 != 0;
            final String _tmpActionResult;
            if (_cursor.isNull(_cursorIndexOfActionResult)) {
              _tmpActionResult = null;
            } else {
              _tmpActionResult = _cursor.getString(_cursorIndexOfActionResult);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpReadAt;
            if (_cursor.isNull(_cursorIndexOfReadAt)) {
              _tmpReadAt = null;
            } else {
              _tmpReadAt = _cursor.getLong(_cursorIndexOfReadAt);
            }
            final Long _tmpActionTakenAt;
            if (_cursor.isNull(_cursorIndexOfActionTakenAt)) {
              _tmpActionTakenAt = null;
            } else {
              _tmpActionTakenAt = _cursor.getLong(_cursorIndexOfActionTakenAt);
            }
            final Long _tmpExpiresAt;
            if (_cursor.isNull(_cursorIndexOfExpiresAt)) {
              _tmpExpiresAt = null;
            } else {
              _tmpExpiresAt = _cursor.getLong(_cursorIndexOfExpiresAt);
            }
            final boolean _tmpSmsRequired;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfSmsRequired);
            _tmpSmsRequired = _tmp_3 != 0;
            final boolean _tmpSmsSent;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfSmsSent);
            _tmpSmsSent = _tmp_4 != 0;
            final boolean _tmpSmsDelivered;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfSmsDelivered);
            _tmpSmsDelivered = _tmp_5 != 0;
            final String _tmpSmsFailureReason;
            if (_cursor.isNull(_cursorIndexOfSmsFailureReason)) {
              _tmpSmsFailureReason = null;
            } else {
              _tmpSmsFailureReason = _cursor.getString(_cursorIndexOfSmsFailureReason);
            }
            _item = new Notification(_tmpId,_tmpRecipientId,_tmpSenderId,_tmpType,_tmpCategory,_tmpPriority,_tmpTitle,_tmpMessage,_tmpActionData,_tmpRelatedEntityId,_tmpRelatedEntityType,_tmpIsRead,_tmpIsActionRequired,_tmpIsActionTaken,_tmpActionResult,_tmpCreatedAt,_tmpReadAt,_tmpActionTakenAt,_tmpExpiresAt,_tmpSmsRequired,_tmpSmsSent,_tmpSmsDelivered,_tmpSmsFailureReason);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }

  private String __NotificationType_enumToString(@NonNull final NotificationType _value) {
    switch (_value) {
      case CUSTOMER_APPROVAL_REQUEST: return "CUSTOMER_APPROVAL_REQUEST";
      case CUSTOMER_APPROVED: return "CUSTOMER_APPROVED";
      case CUSTOMER_REJECTED: return "CUSTOMER_REJECTED";
      case NEW_INVOICE_CREATED: return "NEW_INVOICE_CREATED";
      case INVOICE_PAID: return "INVOICE_PAID";
      case INVOICE_OVERDUE: return "INVOICE_OVERDUE";
      case PAYMENT_RECEIVED: return "PAYMENT_RECEIVED";
      case PAYMENT_CONFIRMED: return "PAYMENT_CONFIRMED";
      case PAYMENT_REJECTED: return "PAYMENT_REJECTED";
      case SETTLEMENT_REQUEST: return "SETTLEMENT_REQUEST";
      case SETTLEMENT_APPROVED: return "SETTLEMENT_APPROVED";
      case SETTLEMENT_REJECTED: return "SETTLEMENT_REJECTED";
      case LOW_STOCK_ALERT: return "LOW_STOCK_ALERT";
      case OUT_OF_STOCK_ALERT: return "OUT_OF_STOCK_ALERT";
      case STOCK_UPDATED: return "STOCK_UPDATED";
      case SYSTEM_MAINTENANCE: return "SYSTEM_MAINTENANCE";
      case SYSTEM_UPDATE: return "SYSTEM_UPDATE";
      case BACKUP_COMPLETED: return "BACKUP_COMPLETED";
      case WELCOME_MESSAGE: return "WELCOME_MESSAGE";
      case REMINDER: return "REMINDER";
      case ANNOUNCEMENT: return "ANNOUNCEMENT";
      case SYSTEM_MESSAGE: return "SYSTEM_MESSAGE";
      default: throw new IllegalArgumentException("Can't convert enum to string, unknown enum value: " + _value);
    }
  }

  private String __NotificationCategory_enumToString(@NonNull final NotificationCategory _value) {
    switch (_value) {
      case CUSTOMER_MANAGEMENT: return "CUSTOMER_MANAGEMENT";
      case SALES_INVOICE: return "SALES_INVOICE";
      case PAYMENT: return "PAYMENT";
      case SETTLEMENT: return "SETTLEMENT";
      case INVENTORY: return "INVENTORY";
      case SYSTEM: return "SYSTEM";
      case GENERAL: return "GENERAL";
      default: throw new IllegalArgumentException("Can't convert enum to string, unknown enum value: " + _value);
    }
  }

  private String __NotificationPriority_enumToString(@NonNull final NotificationPriority _value) {
    switch (_value) {
      case LOW: return "LOW";
      case NORMAL: return "NORMAL";
      case HIGH: return "HIGH";
      case URGENT: return "URGENT";
      case CRITICAL: return "CRITICAL";
      default: throw new IllegalArgumentException("Can't convert enum to string, unknown enum value: " + _value);
    }
  }

  private NotificationType __NotificationType_stringToEnum(@NonNull final String _value) {
    switch (_value) {
      case "CUSTOMER_APPROVAL_REQUEST": return NotificationType.CUSTOMER_APPROVAL_REQUEST;
      case "CUSTOMER_APPROVED": return NotificationType.CUSTOMER_APPROVED;
      case "CUSTOMER_REJECTED": return NotificationType.CUSTOMER_REJECTED;
      case "NEW_INVOICE_CREATED": return NotificationType.NEW_INVOICE_CREATED;
      case "INVOICE_PAID": return NotificationType.INVOICE_PAID;
      case "INVOICE_OVERDUE": return NotificationType.INVOICE_OVERDUE;
      case "PAYMENT_RECEIVED": return NotificationType.PAYMENT_RECEIVED;
      case "PAYMENT_CONFIRMED": return NotificationType.PAYMENT_CONFIRMED;
      case "PAYMENT_REJECTED": return NotificationType.PAYMENT_REJECTED;
      case "SETTLEMENT_REQUEST": return NotificationType.SETTLEMENT_REQUEST;
      case "SETTLEMENT_APPROVED": return NotificationType.SETTLEMENT_APPROVED;
      case "SETTLEMENT_REJECTED": return NotificationType.SETTLEMENT_REJECTED;
      case "LOW_STOCK_ALERT": return NotificationType.LOW_STOCK_ALERT;
      case "OUT_OF_STOCK_ALERT": return NotificationType.OUT_OF_STOCK_ALERT;
      case "STOCK_UPDATED": return NotificationType.STOCK_UPDATED;
      case "SYSTEM_MAINTENANCE": return NotificationType.SYSTEM_MAINTENANCE;
      case "SYSTEM_UPDATE": return NotificationType.SYSTEM_UPDATE;
      case "BACKUP_COMPLETED": return NotificationType.BACKUP_COMPLETED;
      case "WELCOME_MESSAGE": return NotificationType.WELCOME_MESSAGE;
      case "REMINDER": return NotificationType.REMINDER;
      case "ANNOUNCEMENT": return NotificationType.ANNOUNCEMENT;
      case "SYSTEM_MESSAGE": return NotificationType.SYSTEM_MESSAGE;
      default: throw new IllegalArgumentException("Can't convert value to enum, unknown value: " + _value);
    }
  }

  private NotificationCategory __NotificationCategory_stringToEnum(@NonNull final String _value) {
    switch (_value) {
      case "CUSTOMER_MANAGEMENT": return NotificationCategory.CUSTOMER_MANAGEMENT;
      case "SALES_INVOICE": return NotificationCategory.SALES_INVOICE;
      case "PAYMENT": return NotificationCategory.PAYMENT;
      case "SETTLEMENT": return NotificationCategory.SETTLEMENT;
      case "INVENTORY": return NotificationCategory.INVENTORY;
      case "SYSTEM": return NotificationCategory.SYSTEM;
      case "GENERAL": return NotificationCategory.GENERAL;
      default: throw new IllegalArgumentException("Can't convert value to enum, unknown value: " + _value);
    }
  }

  private NotificationPriority __NotificationPriority_stringToEnum(@NonNull final String _value) {
    switch (_value) {
      case "LOW": return NotificationPriority.LOW;
      case "NORMAL": return NotificationPriority.NORMAL;
      case "HIGH": return NotificationPriority.HIGH;
      case "URGENT": return NotificationPriority.URGENT;
      case "CRITICAL": return NotificationPriority.CRITICAL;
      default: throw new IllegalArgumentException("Can't convert value to enum, unknown value: " + _value);
    }
  }
}
